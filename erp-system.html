<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>白墨打印统计系统</title>
    <!-- SheetJS库用于处理Excel文件 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: #001529;
            color: white;
            padding: 0;
        }

        .logo {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #002140;
        }

        .logo h1 {
            font-size: 18px;
            font-weight: 600;
        }

        .menu {
            list-style: none;
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s;
            border-left: 3px solid transparent;
        }

        .menu-item:hover {
            background: #1890ff;
        }

        .menu-item.active {
            background: #1890ff;
            border-left-color: #40a9ff;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page {
            display: none;
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .page.active {
            display: block;
        }

        .page-header {
            margin-bottom: 24px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 16px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .page-description {
            color: #666;
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-card h3 {
            font-size: 14px;
            margin-bottom: 8px;
            opacity: 0.9;
        }

        .stat-card .value {
            font-size: 28px;
            font-weight: 600;
        }

        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 16px;
            background: #fafafa;
            border-radius: 6px;
        }

        .search-box {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 200px;
        }

        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #40a9ff;
        }

        .btn-danger {
            background: #ff4d4f;
        }

        .btn-danger:hover {
            background: #ff7875;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .table th {
            background: #fafafa;
            font-weight: 600;
        }

        .table tr:hover {
            background: #f5f5f5;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            padding: 24px;
            border-radius: 8px;
            width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }

        .form-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
            margin-top: 24px;
        }

        .status-normal {
            color: #52c41a;
            background: #f6ffed;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .status-low {
            color: #faad14;
            background: #fffbe6;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        /* 可搜索下拉框样式 */
        .searchable-select {
            position: relative;
            width: 100%;
        }

        .searchable-select-input {
            width: 100%;
            padding: 8px 30px 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }

        .searchable-select-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .searchable-select-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
            color: #999;
            transition: transform 0.2s;
        }

        .searchable-select.open .searchable-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .searchable-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d9d9d9;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .searchable-select.open .searchable-select-dropdown {
            display: block;
        }

        .searchable-select-option {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
        }

        .searchable-select-option:hover {
            background: #f5f5f5;
        }

        .searchable-select-option.selected {
            background: #e6f7ff;
            color: #1890ff;
        }

        .searchable-select-option:last-child {
            border-bottom: none;
        }

        .searchable-select-no-results {
            padding: 8px 12px;
            color: #999;
            text-align: center;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="logo">
                <h1>白墨打印统计系统</h1>
            </div>
            <ul class="menu">
                <li class="menu-item active" onclick="showPage('dashboard', this)">📊 仪表板</li>
                <li class="menu-item" onclick="showPage('orders', this)">📋 订单管理</li>
                <li class="menu-item" onclick="showPage('customers', this)">🤝 客户管理</li>
                <li class="menu-item" onclick="showPage('patterns', this)">🎨 图案数据库</li>
                <li class="menu-item" onclick="showPage('finance', this)">💰 财务管理</li>
                <li class="menu-item" onclick="showPage('materials', this)">📦 耗材管理</li>
                <li class="menu-item" onclick="showPage('settings', this)">⚙️ 基础设置</li>
            </ul>
        </div>

        <div class="main-content">
            <!-- 仪表板页面 -->
            <div id="dashboard" class="page active">
                <div class="page-header">
                    <h2 class="page-title">仪表板</h2>
                    <p class="page-description">打印业务数据概览和快捷操作</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>今日订单</h3>
                        <div class="value" id="todayOrders">0</div>
                    </div>
                    <div class="stat-card">
                        <h3>今日收入</h3>
                        <div class="value" id="todayIncome">¥0</div>
                    </div>
                    <div class="stat-card">
                        <h3>本月订单</h3>
                        <div class="value" id="monthOrders">0</div>
                    </div>
                    <div class="stat-card">
                        <h3>本月收入</h3>
                        <div class="value" id="monthIncome">¥0</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h3 style="margin-bottom: 16px;">快捷操作</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                            <button class="btn" onclick="showPage('orders')">新建订单</button>
                            <button class="btn" onclick="showPage('customers')">客户管理</button>
                            <button class="btn" onclick="showPage('patterns')">图案库</button>
                            <button class="btn" onclick="showPage('finance')">财务分析</button>
                        </div>
                    </div>
                    <div>
                        <h3 style="margin-bottom: 16px;">最近订单</h3>
                        <div id="recentOrders" style="font-size: 14px; line-height: 1.6;">
                            <!-- 最近订单将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单管理页面 -->
            <div id="orders" class="page">
                <div class="page-header">
                    <h2 class="page-title">订单管理</h2>
                    <p class="page-description">管理打印订单、计算价格和跟踪订单状态</p>
                </div>

                <div class="toolbar">
                    <div>
                        <input type="text" class="search-box" placeholder="搜索客户姓名..." id="orderSearch">
                        <select style="margin-left: 8px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" id="colorFilter">
                            <option value="">全部色号</option>
                            <option value="单色">单色</option>
                            <option value="双色">双色</option>
                            <option value="三色">三色</option>
                        </select>

                    </div>
                    <button class="btn" onclick="showOrderModal()">新建订单</button>
                </div>

                <table class="table" id="ordersTable">
                    <thead>
                        <tr>
                            <th>客户姓名</th>
                            <th>图案名称</th>
                            <th>色号</th>
                            <th>计费方式</th>
                            <th>数量</th>
                            <th>单价</th>
                            <th>总价格</th>
                            <th>时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="ordersTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 客户管理页面 -->
            <div id="customers" class="page">
                <div class="page-header">
                    <h2 class="page-title">客户管理</h2>
                    <p class="page-description">管理客户信息、单价设置和联系方式</p>
                </div>

                <!-- 客户统计概览 -->
                <div style="margin-bottom: 24px;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 20px;">
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4 style="color: #1890ff; margin-bottom: 8px;">👥 客户总数</h4>
                            <div style="font-size: 24px; color: #1890ff; margin-bottom: 4px;" id="totalCustomers">0</div>
                            <div style="font-size: 12px; color: #666;">注册客户数量</div>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4 style="color: #52c41a; margin-bottom: 8px;">💰 总余额</h4>
                            <div style="font-size: 24px; color: #52c41a; margin-bottom: 4px;" id="totalBalance">¥0</div>
                            <div style="font-size: 12px; color: #666;">所有客户余额</div>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4 style="color: #ff4d4f; margin-bottom: 8px;">🚨 欠款总额</h4>
                            <div style="font-size: 24px; color: #ff4d4f; margin-bottom: 4px;" id="totalDebt">¥0</div>
                            <div style="font-size: 12px; color: #666;">客户欠款总计</div>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4 style="color: #faad14; margin-bottom: 8px;">📊 平均单价</h4>
                            <div style="font-size: 24px; color: #faad14; margin-bottom: 4px;" id="avgPrice">¥0</div>
                            <div style="font-size: 12px; color: #666;">客户平均单价</div>
                        </div>
                    </div>
                </div>

                <!-- 工具栏和筛选 -->
                <div class="toolbar" style="margin-bottom: 20px;">
                    <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
                        <input type="text" class="search-box" placeholder="搜索客户姓名..." id="customerSearch" style="min-width: 200px;">
                        <select id="customerBalanceFilter" style="padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" onchange="renderCustomersCards()">
                            <option value="all">全部客户</option>
                            <option value="debt">欠款客户</option>
                            <option value="low">余额不足</option>
                            <option value="normal">余额偏低</option>
                            <option value="good">余额充足</option>
                        </select>
                        <select id="customerSortBy" style="padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" onchange="renderCustomersCards()">
                            <option value="name">按姓名排序</option>
                            <option value="balance">按余额排序</option>
                            <option value="price">按单价排序</option>
                            <option value="recent">按最近订单</option>
                        </select>
                        <button class="btn" style="background: #722ed1; color: white;" onclick="toggleCustomerView()">
                            <span id="viewToggleText">📋 表格视图</span>
                        </button>
                    </div>
                    <button class="btn" onclick="showCustomerModal()">➕ 添加客户</button>
                </div>

                <!-- 卡片视图 -->
                <div id="customersCards" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 20px;">
                    <!-- 客户卡片将通过JavaScript动态加载 -->
                </div>

                <!-- 表格视图 -->
                <div id="customersTableContainer" style="display: none;">
                    <table class="table" id="customersTable">
                        <thead>
                            <tr>
                                <th>客户姓名</th>
                                <th>客户单价</th>
                                <th>账户余额</th>
                                <th>最近订单</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="customersTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 客户详情页面 -->
            <div id="customerDetail" class="page">
                <div class="page-header">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h2 class="page-title" id="customerDetailTitle">客户详情</h2>
                            <p class="page-description" id="customerDetailDescription">查看客户完整信息和订单记录</p>
                        </div>
                        <button class="btn" onclick="showPage('customers')" style="background: #d9d9d9; color: #333;">
                            ← 返回客户管理
                        </button>
                    </div>
                </div>

                <!-- 客户基本信息卡片 -->
                <div style="margin-bottom: 24px;">
                    <div style="background: white; padding: 24px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                            <div style="text-align: center; padding: 16px; background: #f8f9fa; border-radius: 8px;">
                                <h3 style="margin: 0 0 8px 0; color: #1890ff;" id="customerDetailName">客户姓名</h3>
                                <div style="font-size: 14px; color: #666;">客户信息</div>
                            </div>
                            <div style="text-align: center; padding: 16px; background: #f8f9fa; border-radius: 8px;">
                                <h3 style="margin: 0 0 8px 0; color: #722ed1;" id="customerDetailPrice">¥0.00</h3>
                                <div style="font-size: 14px; color: #666;">客户单价/个</div>
                            </div>
                            <div style="text-align: center; padding: 16px; background: #f8f9fa; border-radius: 8px;">
                                <h3 style="margin: 0 0 8px 0;" id="customerDetailBalance">¥0.00</h3>
                                <div style="font-size: 14px; color: #666;">账户余额</div>
                            </div>
                            <div style="text-align: center; padding: 16px; background: #f8f9fa; border-radius: 8px;">
                                <h3 style="margin: 0 0 8px 0; color: #52c41a;" id="customerDetailTotalOrders">0</h3>
                                <div style="font-size: 14px; color: #666;">历史订单数</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 财务统计 -->
                <div style="margin-bottom: 24px;">
                    <h3 style="margin-bottom: 16px; color: #333;">💰 财务统计</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
                        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid #52c41a;">
                            <h4 style="margin: 0 0 8px 0; color: #52c41a;">今日订单金额</h4>
                            <div style="font-size: 24px; font-weight: bold; color: #52c41a;" id="customerTodayTotal">¥0.00</div>
                            <div style="font-size: 12px; color: #666; margin-top: 4px;" id="customerTodayCount">今日订单: 0 个</div>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid #1890ff;">
                            <h4 style="margin: 0 0 8px 0; color: #1890ff;">累计消费金额</h4>
                            <div style="font-size: 24px; font-weight: bold; color: #1890ff;" id="customerTotalSpent">¥0.00</div>
                            <div style="font-size: 12px; color: #666; margin-top: 4px;" id="customerAvgOrder">平均订单: ¥0.00</div>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid #722ed1;">
                            <h4 style="margin: 0 0 8px 0; color: #722ed1;">本月消费</h4>
                            <div style="font-size: 24px; font-weight: bold; color: #722ed1;" id="customerMonthTotal">¥0.00</div>
                            <div style="font-size: 12px; color: #666; margin-top: 4px;" id="customerMonthCount">本月订单: 0 个</div>
                        </div>
                    </div>
                </div>

                <!-- 订单记录 -->
                <div style="margin-bottom: 24px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <h3 style="margin: 0; color: #333;">📋 订单记录</h3>
                        <div style="display: flex; gap: 12px; align-items: center;">
                            <select id="customerOrderFilter" style="padding: 6px 12px; border: 1px solid #d9d9d9; border-radius: 4px;" onchange="renderCustomerOrders()">
                                <option value="all">全部订单</option>
                                <option value="today">今日订单</option>
                                <option value="week">本周订单</option>
                                <option value="month">本月订单</option>
                            </select>
                            <input type="text" id="customerOrderSearch" placeholder="搜索图案名称..." style="padding: 6px 12px; border: 1px solid #d9d9d9; border-radius: 4px; width: 200px;" oninput="renderCustomerOrders()">
                        </div>
                    </div>

                    <div style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden;">
                        <table class="table" style="margin: 0;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th>订单时间</th>
                                    <th>图案名称</th>
                                    <th>色号</th>
                                    <th>计费方式</th>
                                    <th>数量</th>
                                    <th>每平方数量</th>
                                    <th>图案单价</th>
                                    <th>订单金额</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="customerOrdersTableBody">
                                <!-- 订单数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <h4 style="margin: 0 0 16px 0; color: #333;">快捷操作</h4>
                    <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                        <button class="btn" style="background: #52c41a; color: white;" onclick="quickRechargeFromDetail()">💰 客户充值</button>
                        <button class="btn" style="background: #ff4d4f; color: white;" onclick="addDebtFromDetail()">📝 增加欠款</button>
                        <button class="btn" style="background: #1890ff; color: white;" onclick="editCustomerFromDetail()">✏️ 编辑客户</button>
                        <button class="btn" style="background: #fa8c16; color: white;" onclick="exportCustomerReport()">📊 导出报表</button>
                        <button class="btn" style="background: #722ed1; color: white;" onclick="showPage('orders'); showOrderModal(); preSelectCustomer()">➕ 新建订单</button>
                    </div>
                </div>
            </div>

            <!-- 图案数据库页面 -->
            <div id="patterns" class="page">
                <div class="page-header">
                    <h2 class="page-title">图案数据库</h2>
                    <p class="page-description">管理打印图案的基础信息和参数</p>
                </div>

                <div class="toolbar">
                    <div>
                        <input type="text" class="search-box" placeholder="搜索图案名称..." id="patternSearch">
                        <select style="margin-left: 8px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" id="patternColorFilter">
                            <option value="">全部色号</option>
                            <option value="单色">单色</option>
                            <option value="双色">双色</option>
                            <option value="三色">三色</option>
                        </select>
                    </div>
                    <button class="btn" onclick="showPatternModal()">添加图案</button>
                </div>

                <table class="table" id="patternsTable">
                    <thead>
                        <tr>
                            <th>图案名称</th>
                            <th>色号</th>
                            <th>行数量</th>
                            <th>实际高度</th>
                            <th>添加出血</th>
                            <th>每平方数量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="patternsTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 财务管理页面 -->
            <div id="finance" class="page">
                <div class="page-header">
                    <h2 class="page-title">财务管理</h2>
                    <p class="page-description">财务分析和客户充值管理</p>
                </div>

                <div style="margin-bottom: 24px;">
                    <h3 style="margin-bottom: 16px;">📊 财务分析</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 20px;">
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>今日收入</h4>
                            <div style="font-size: 24px; color: #52c41a; margin-top: 8px;" id="financeToday">¥0</div>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>本月收入</h4>
                            <div style="font-size: 24px; color: #1890ff; margin-top: 8px;" id="financeMonth">¥0</div>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>本年收入</h4>
                            <div style="font-size: 24px; color: #722ed1; margin-top: 8px;" id="financeYear">¥0</div>
                        </div>
                    </div>
                </div>

                <!-- 客户充值管理 -->
                <div style="margin-bottom: 32px;">
                    <h3 style="margin-bottom: 16px;">💰 客户充值管理</h3>
                    <div style="background: #f9f9f9; padding: 20px; border-radius: 8px;">
                        <div style="display: flex; gap: 12px; margin-bottom: 20px; flex-wrap: wrap;">
                            <button class="btn" onclick="showRechargeModal()">客户充值</button>
                            <button class="btn" style="background: #52c41a; color: white;" onclick="showBalanceReport()">余额报表</button>
                            <button class="btn" style="background: #722ed1; color: white;" onclick="showRechargeHistory()">充值记录</button>
                        </div>

                        <!-- 余额预警 -->
                        <div id="balanceWarning" style="margin-bottom: 16px;">
                            <!-- 余额不足提醒将在这里显示 -->
                        </div>

                        <!-- 客户余额快速查看 -->
                        <div style="margin-bottom: 16px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                <h4 style="margin: 0; color: #333;">客户余额概览</h4>
                                <div style="display: flex; gap: 16px; align-items: center;">
                                    <select id="balanceFilter" style="padding: 4px 8px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;" onchange="updateCustomerBalances()">
                                        <option value="all">全部客户</option>
                                        <option value="debt">欠款客户</option>
                                        <option value="low">余额不足</option>
                                        <option value="normal">余额偏低</option>
                                        <option value="good">余额充足</option>
                                    </select>
                                    <div id="balanceSummary" style="display: flex; gap: 16px; font-size: 14px;">
                                        <!-- 余额统计将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                            <div id="customerBalances" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 16px;">
                                <!-- 客户余额卡片将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 财务报表 -->
                <div style="margin-bottom: 32px;">
                    <h3 style="margin-bottom: 16px;">📊 财务报表</h3>
                    <div style="background: #f9f9f9; padding: 20px; border-radius: 8px;">
                        <div style="display: flex; gap: 12px; margin-bottom: 20px; flex-wrap: wrap;">
                            <button class="btn" onclick="showIncomeReport()">收入明细</button>
                            <button class="btn" onclick="showCustomerRanking()">客户排行</button>
                            <button class="btn" onclick="showPatternRanking()">图案销量</button>
                            <button class="btn" style="background: #fa8c16; color: white;" onclick="exportFinanceReport()">导出报表</button>
                        </div>

                        <!-- 报表显示区域 -->
                        <div id="financeReportArea" style="display: none;">
                            <!-- 报表内容将在这里显示 -->
                        </div>
                    </div>
                </div>

                <!-- 成本分析 -->
                <div>
                    <h3 style="margin-bottom: 16px;">💹 成本分析</h3>
                    <div style="background: #f9f9f9; padding: 20px; border-radius: 8px;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                            <div style="background: white; padding: 16px; border-radius: 6px; text-align: center;">
                                <h4 style="color: #1890ff;">耗材成本</h4>
                                <div style="font-size: 20px; margin: 8px 0;" id="materialCost">¥0</div>
                                <div style="font-size: 12px; color: #666;">本月耗材支出</div>
                            </div>
                            <div style="background: white; padding: 16px; border-radius: 6px; text-align: center;">
                                <h4 style="color: #52c41a;">毛利润</h4>
                                <div style="font-size: 20px; margin: 8px 0;" id="grossProfit">¥0</div>
                                <div style="font-size: 12px; color: #666;">收入 - 耗材成本</div>
                            </div>
                            <div style="background: white; padding: 16px; border-radius: 6px; text-align: center;">
                                <h4 style="color: #722ed1;">利润率</h4>
                                <div style="font-size: 20px; margin: 8px 0;" id="profitRate">0%</div>
                                <div style="font-size: 12px; color: #666;">毛利润 / 收入</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 耗材管理页面 -->
            <div id="materials" class="page">
                <div class="page-header">
                    <h2 class="page-title">耗材管理</h2>
                    <p class="page-description">管理打印耗材库存和采购</p>
                </div>

                <div class="toolbar">
                    <div>
                        <input type="text" class="search-box" placeholder="搜索耗材名称..." id="materialSearch">
                        <select style="margin-left: 8px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;" id="categoryFilter">
                            <option value="">全部分类</option>
                            <option value="打印纸张">打印纸张</option>
                            <option value="墨水耗材">墨水耗材</option>
                            <option value="设备配件">设备配件</option>
                            <option value="清洗用品">清洗用品</option>
                        </select>
                    </div>
                    <button class="btn" onclick="showMaterialModal()">添加耗材</button>
                </div>

                <table class="table" id="materialsTable">
                    <thead>
                        <tr>
                            <th>耗材名称</th>
                            <th>分类</th>
                            <th>单位</th>
                            <th>单价</th>
                            <th>库存</th>
                            <th>状态</th>
                            <th>供应商</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="materialsTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 基础设置页面 -->
            <div id="settings" class="page">
                <div class="page-header">
                    <h2 class="page-title">基础设置</h2>
                    <p class="page-description">系统设置、数据管理和打印参数配置</p>
                </div>

                <!-- 数据管理 -->
                <div style="margin-bottom: 32px;">
                    <h3 style="margin-bottom: 16px;">📁 数据管理</h3>
                    <div style="background: #f9f9f9; padding: 20px; border-radius: 8px;">
                        <p style="margin-bottom: 16px; color: #666;">
                            为了防止数据丢失，建议定期备份数据。您可以导出数据到本地文件，也可以从备份文件恢复数据。
                        </p>
                        <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                            <button class="btn" onclick="exportData()">📤 导出数据</button>
                            <button class="btn" onclick="document.getElementById('importFile').click()">📥 导入数据</button>
                            <button class="btn btn-danger" onclick="clearAllData()">🗑️ 清空数据</button>
                        </div>
                        <input type="file" id="importFile" accept=".json" style="display: none;" onchange="importData(event)">
                        <div style="margin-top: 12px; font-size: 12px; color: #999;">
                            数据格式：JSON文件 | 包含：订单、客户、图案、耗材等所有数据
                        </div>
                    </div>
                </div>

                <!-- 批量导入 -->
                <div style="margin-bottom: 32px;">
                    <h3 style="margin-bottom: 16px;">📊 批量导入</h3>
                    <div style="background: #f9f9f9; padding: 20px; border-radius: 8px;">
                        <p style="margin-bottom: 16px; color: #666;">
                            支持批量导入客户、订单和图案数据。请先下载对应的模板文件，按格式填写数据后上传。
                        </p>

                        <!-- 客户批量导入 -->
                        <div style="margin-bottom: 20px; padding: 16px; background: white; border-radius: 6px; border: 1px solid #e8e8e8;">
                            <h4 style="margin-bottom: 12px; color: #1890ff;">👥 客户批量导入</h4>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap; margin-bottom: 8px;">
                                <button class="btn" style="background: #52c41a; color: white;" onclick="downloadCustomerTemplate()">📥 下载客户模板</button>
                                <button class="btn" onclick="document.getElementById('customerBatchFile').click()">📤 上传客户数据</button>
                            </div>
                            <input type="file" id="customerBatchFile" accept=".xlsx,.xls" style="display: none;" onchange="importCustomerBatch(event)">
                            <div style="font-size: 12px; color: #999;">
                                支持格式：Excel文件(.xlsx/.xls) | 字段：客户姓名,客户单价
                            </div>
                        </div>

                        <!-- 图案批量导入 -->
                        <div style="margin-bottom: 20px; padding: 16px; background: white; border-radius: 6px; border: 1px solid #e8e8e8;">
                            <h4 style="margin-bottom: 12px; color: #722ed1;">🎨 图案批量导入</h4>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap; margin-bottom: 8px;">
                                <button class="btn" style="background: #52c41a; color: white;" onclick="downloadPatternTemplate()">📥 下载图案模板</button>
                                <button class="btn" onclick="document.getElementById('patternBatchFile').click()">📤 上传图案数据</button>
                            </div>
                            <input type="file" id="patternBatchFile" accept=".xlsx,.xls" style="display: none;" onchange="importPatternBatch(event)">
                            <div style="font-size: 12px; color: #999;">
                                支持格式：Excel文件(.xlsx/.xls) | 字段：图案名称,色号,行数量,实际高度
                            </div>
                        </div>

                        <!-- 订单批量导入 -->
                        <div style="margin-bottom: 20px; padding: 16px; background: white; border-radius: 6px; border: 1px solid #e8e8e8;">
                            <h4 style="margin-bottom: 12px; color: #fa8c16;">📋 订单批量导入</h4>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap; margin-bottom: 8px;">
                                <button class="btn" style="background: #52c41a; color: white;" onclick="downloadOrderTemplate()">📥 下载订单模板</button>
                                <button class="btn" onclick="document.getElementById('orderBatchFile').click()">📤 上传订单数据</button>
                            </div>
                            <input type="file" id="orderBatchFile" accept=".xlsx,.xls" style="display: none;" onchange="importOrderBatch(event)">
                            <div style="font-size: 12px; color: #999;">
                                支持格式：Excel文件(.xlsx/.xls) | 字段：客户姓名,图案名称,计费方式,数量,创建时间
                            </div>
                        </div>

                        <!-- 导入结果显示 -->
                        <div id="importResult" style="display: none; margin-top: 16px; padding: 12px; border-radius: 4px;">
                            <!-- 导入结果将在这里显示 -->
                        </div>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div style="margin-bottom: 32px;">
                    <h3 style="margin-bottom: 16px;">ℹ️ 系统信息</h3>
                    <div style="background: #f9f9f9; padding: 20px; border-radius: 8px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div>
                                <strong>系统版本：</strong> v1.0.0<br>
                                <strong>数据存储：</strong> 浏览器本地存储 + 文件备份<br>
                                <strong>最后备份：</strong> <span id="lastBackupTime">未备份</span>
                            </div>
                            <div>
                                <strong>数据统计：</strong><br>
                                <span id="dataStats">正在统计...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 打印参数设置 -->
                <div>
                    <h3 style="margin-bottom: 16px;">⚙️ 打印参数设置</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>📏 基础参数</h4>
                            <p style="color: #666; font-size: 14px;">清洗出品 = 实际高度 + 20mm</p>
                            <button class="btn" style="margin-top: 8px;" onclick="alert('参数设置功能开发中')">参数设置</button>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>🎨 色号管理</h4>
                            <p style="color: #666; font-size: 14px;">单色、双色、三色设置</p>
                            <button class="btn" style="margin-top: 8px;" onclick="alert('色号管理功能开发中')">色号设置</button>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>📐 计算公式</h4>
                            <p style="color: #666; font-size: 14px;">客户方数量 = 1000 / 清洗出品 / 行数量</p>
                            <button class="btn" style="margin-top: 8px;" onclick="alert('公式设置功能开发中')">公式设置</button>
                        </div>
                        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                            <h4>🖨️ 打印设置</h4>
                            <p style="color: #666; font-size: 14px;">价格计算和打印参数</p>
                            <button class="btn" style="margin-top: 8px;" onclick="alert('打印设置功能开发中')">打印设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单编辑模态框 -->
    <div id="orderModal" class="modal">
        <div class="modal-content">
            <h3 id="orderModalTitle">新建订单</h3>
            <form id="orderForm">
                <div class="form-group">
                    <label>客户姓名 *</label>
                    <select id="orderCustomerName" required>
                        <option value="">请选择客户</option>
                        <!-- 客户选项将通过JavaScript动态加载 -->
                    </select>
                </div>
                <div class="form-group">
                    <label>选择图案 *</label>
                    <div class="searchable-select" id="orderPatternSelect">
                        <input type="text" class="searchable-select-input" id="orderPatternName" placeholder="搜索或选择图案..." readonly required>
                        <span class="searchable-select-arrow">▼</span>
                        <div class="searchable-select-dropdown" id="orderPatternDropdown">
                            <!-- 图案选项将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
                <div class="form-group" style="display: none;">
                    <label>色号 (自动填充)</label>
                    <input type="text" id="orderColorType" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group" style="display: none;">
                    <label>行数量 (自动填充)</label>
                    <input type="number" id="orderRowCount" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group" style="display: none;">
                    <label>实际高度(mm) (自动填充)</label>
                    <input type="number" id="orderActualHeight" step="0.1" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group" style="display: none;">
                    <label>添加出血(mm) (自动填充)</label>
                    <input type="number" id="orderBleedHeight" step="0.1" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group" style="display: none;">
                    <label>每平方数量 (自动计算)</label>
                    <input type="number" id="orderCustomerSquare" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group">
                    <label>计费方式 *</label>
                    <div style="margin-top: 8px;">
                        <label style="display: inline-block; margin-right: 20px;">
                            <input type="radio" name="billingType" value="pieces" onchange="changeBillingType()" checked> 按个数计费
                        </label>
                        <label style="display: inline-block;">
                            <input type="radio" name="billingType" value="square" onchange="changeBillingType()"> 按平方计费
                        </label>
                    </div>
                </div>
                <div class="form-group" id="piecesGroup">
                    <label>数量(个) *</label>
                    <input type="number" id="orderPiecesCount" min="0" value="0" onchange="calculateOrderPrice()">
                    <div style="font-size: 12px; color: #666; margin-top: 4px;">
                        个数单价 = 客户单价 ÷ 每平方数量
                    </div>
                </div>
                <div class="form-group" id="squareGroup" style="display: none;">
                    <label>面积(平方) *</label>
                    <input type="number" id="orderSquareCount" step="0.01" min="0" value="0" onchange="calculateOrderPrice()">
                    <div style="font-size: 12px; color: #666; margin-top: 4px;">
                        平方单价 = 16元/平方
                    </div>
                </div>
                <div class="form-group">
                    <label>单价 (自动计算)</label>
                    <input type="number" id="orderUnitPrice" step="0.01" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group">
                    <label>总价格 (自动计算)</label>
                    <input type="number" id="orderTotalPrice" step="0.01" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group">
                    <label>订单日期 *</label>
                    <input type="date" id="orderDate" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" style="background: #d9d9d9; color: #333;" onclick="closeOrderModal()">取消</button>
                    <button type="submit" class="btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 客户管理编辑模态框 -->
    <div id="customerModal" class="modal">
        <div class="modal-content">
            <h3 id="customerModalTitle">添加客户</h3>
            <form id="customerForm">
                <div class="form-group">
                    <label>客户姓名 *</label>
                    <input type="text" id="customerName" required>
                </div>
                <div class="form-group">
                    <label>客户单价 *</label>
                    <input type="number" id="customerUnitPrice" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label>初始余额</label>
                    <input type="number" id="customerBalance" step="0.01" min="0" value="0" placeholder="新客户初始余额">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" style="background: #d9d9d9; color: #333;" onclick="closeCustomerModal()">取消</button>
                    <button type="submit" class="btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 图案数据库编辑模态框 -->
    <div id="patternModal" class="modal">
        <div class="modal-content">
            <h3 id="patternModalTitle">添加图案</h3>
            <form id="patternForm">
                <div class="form-group">
                    <label>图案名称 *</label>
                    <input type="text" id="patternName" required>
                </div>
                <div class="form-group">
                    <label>色号 *</label>
                    <select id="patternColorType" required>
                        <option value="">请选择色号</option>
                        <option value="单色">单色</option>
                        <option value="双色">双色</option>
                        <option value="三色">三色</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>行数量 *</label>
                    <input type="number" id="patternRowCount" min="1" required onchange="calculatePatternData()">
                </div>
                <div class="form-group">
                    <label>实际高度(mm) *</label>
                    <input type="number" id="patternActualHeight" step="0.1" min="0" required onchange="calculatePatternData()">
                </div>
                <div class="form-group">
                    <label>添加出血(mm) (自动计算)</label>
                    <input type="number" id="patternBleedHeight" step="0.1" readonly style="background: #f5f5f5;">
                    <div style="font-size: 12px; color: #666; margin-top: 4px;">
                        添加出血 = 实际高度 + 20mm
                    </div>
                </div>
                <div class="form-group">
                    <label>每平方数量 (自动计算)</label>
                    <input type="number" id="patternCustomerSquare" readonly style="background: #f5f5f5;">
                    <div style="font-size: 12px; color: #666; margin-top: 4px;">
                        每平方数量 = (1600mm ÷ 添加出血) × 行数量 (取整数)
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" style="background: #d9d9d9; color: #333;" onclick="closePatternModal()">取消</button>
                    <button type="submit" class="btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 客户充值模态框 -->
    <div id="rechargeModal" class="modal">
        <div class="modal-content">
            <h3 id="rechargeModalTitle">客户充值</h3>
            <form id="rechargeForm">
                <div class="form-group">
                    <label>选择客户 *</label>
                    <select id="rechargeCustomer" required>
                        <option value="">请选择客户</option>
                        <!-- 客户选项将通过JavaScript动态加载 -->
                    </select>
                </div>
                <div class="form-group">
                    <label>当前余额</label>
                    <input type="text" id="currentBalance" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group">
                    <label>充值金额 *</label>
                    <input type="number" id="rechargeAmount" step="0.01" min="0.01" required placeholder="请输入充值金额">
                </div>
                <div class="form-group">
                    <label>充值说明</label>
                    <input type="text" id="rechargeDescription" placeholder="充值说明（可选）">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" style="background: #d9d9d9; color: #333;" onclick="closeRechargeModal()">取消</button>
                    <button type="submit" class="btn">确认充值</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 增加欠款模态框 -->
    <div id="debtModal" class="modal">
        <div class="modal-content">
            <h3 id="debtModalTitle">增加客户欠款</h3>
            <form id="debtForm">
                <div class="form-group">
                    <label>选择客户 *</label>
                    <select id="debtCustomer" required>
                        <option value="">请选择客户</option>
                        <!-- 客户选项将通过JavaScript动态加载 -->
                    </select>
                </div>
                <div class="form-group">
                    <label>当前余额</label>
                    <input type="text" id="currentDebt" readonly style="background: #f5f5f5;">
                </div>
                <div class="form-group">
                    <label>欠款金额 *</label>
                    <input type="number" id="debtAmount" step="0.01" min="0.01" required placeholder="请输入欠款金额">
                </div>
                <div class="form-group">
                    <label>欠款原因 *</label>
                    <textarea id="debtDescription" rows="3" placeholder="请输入欠款原因..." required></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" style="background: #d9d9d9; color: #333;" onclick="closeDebtModal()">取消</button>
                    <button type="submit" class="btn" style="background: #ff4d4f; color: white;">确认增加欠款</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 财务报表模态框 -->
    <div id="financeReportModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <h3 id="financeReportModalTitle">财务报表</h3>
            <div id="financeReportContent">
                <!-- 报表内容将在这里显示 -->
            </div>
            <div class="form-actions">
                <button type="button" class="btn" onclick="closeFinanceReportModal()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 耗材编辑模态框 -->
    <div id="materialModal" class="modal">
        <div class="modal-content">
            <h3 id="materialModalTitle">添加耗材</h3>
            <form id="materialForm">
                <div class="form-group">
                    <label>耗材名称 *</label>
                    <input type="text" id="materialName" required>
                </div>
                <div class="form-group">
                    <label>分类 *</label>
                    <select id="materialCategory" required>
                        <option value="">请选择分类</option>
                        <option value="打印纸张">打印纸张</option>
                        <option value="墨水耗材">墨水耗材</option>
                        <option value="设备配件">设备配件</option>
                        <option value="清洗用品">清洗用品</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>单位 *</label>
                    <input type="text" id="materialUnit" placeholder="如：张、瓶、个等" required>
                </div>
                <div class="form-group">
                    <label>单价 *</label>
                    <input type="number" id="materialPrice" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label>库存数量 *</label>
                    <input type="number" id="materialStock" min="0" required>
                </div>
                <div class="form-group">
                    <label>供应商</label>
                    <input type="text" id="materialSupplier">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn" style="background: #d9d9d9; color: #333;" onclick="closeMaterialModal()">取消</button>
                    <button type="submit" class="btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 打印订单数据
        let orders = [
            {
                id: 1,
                customerName: '张三',
                patternName: '标准花纹A',
                colorType: '单色',
                rowCount: 10,
                actualHeight: 50.0,
                bleedHeight: 70.0,
                customerSquare: 228,
                billingType: 'pieces',
                piecesCount: 100,
                squareCount: 0,
                unitPrice: 10.96,
                totalPrice: 1096.0,
                createTime: '2025-01-28 10:30'
            },
            {
                id: 2,
                customerName: '李四',
                patternName: '复杂图案B',
                colorType: '双色',
                rowCount: 8,
                actualHeight: 60.0,
                bleedHeight: 80.0,
                customerSquare: 160,
                billingType: 'square',
                piecesCount: 0,
                squareCount: 3.0,
                unitPrice: 16.0,
                totalPrice: 48.0,
                createTime: '2025-01-28 14:20'
            },
            {
                id: 3,
                customerName: '王五',
                patternName: '精细图案C',
                colorType: '三色',
                rowCount: 12,
                actualHeight: 45.0,
                bleedHeight: 65.0,
                customerSquare: 295,
                billingType: 'pieces',
                piecesCount: 80,
                squareCount: 0,
                unitPrice: 9.49,
                totalPrice: 759.2,
                createTime: '2025-01-28 16:45'
            }
        ];

        // 客户管理数据
        let customers = [
            {
                id: 1,
                name: '张三',
                unitPrice: 2.5,
                balance: 500.00
            },
            {
                id: 2,
                name: '李四',
                unitPrice: 3.0,
                balance: 80.00  // 余额不足
            },
            {
                id: 3,
                name: '王五',
                unitPrice: 2.8,
                balance: -150.00  // 欠款
            },
            {
                id: 4,
                name: '赵六',
                unitPrice: 2.6,
                balance: 250.00  // 余额偏低
            }
        ];

        // 充值记录数据
        let rechargeRecords = [
            {
                id: 1,
                customerId: 1,
                customerName: '张三',
                amount: 500.00,
                type: 'recharge', // recharge: 充值, consume: 消费
                description: '账户充值',
                createTime: '2025-01-28 09:00:00'
            },
            {
                id: 2,
                customerId: 2,
                customerName: '李四',
                amount: 800.00,
                type: 'recharge',
                description: '账户充值',
                createTime: '2025-01-28 10:00:00'
            }
        ];

        // 图案数据库
        let patterns = [
            {
                id: 1,
                name: '标准花纹A',
                colorType: '单色',
                rowCount: 10,
                actualHeight: 50.0,
                bleedHeight: 70.0,
                customerSquare: 228
            },
            {
                id: 2,
                name: '复杂图案B',
                colorType: '双色',
                rowCount: 8,
                actualHeight: 60.0,
                bleedHeight: 80.0,
                customerSquare: 160
            },
            {
                id: 3,
                name: '精细图案C',
                colorType: '三色',
                rowCount: 12,
                actualHeight: 45.0,
                bleedHeight: 65.0,
                customerSquare: 295
            },
            {
                id: 4,
                name: '玫瑰花纹',
                colorType: '单色',
                rowCount: 8,
                actualHeight: 45.0,
                bleedHeight: 65.0,
                customerSquare: 196
            },
            {
                id: 5,
                name: '蝴蝶图案',
                colorType: '三色',
                rowCount: 15,
                actualHeight: 40.0,
                bleedHeight: 60.0,
                customerSquare: 400
            },
            {
                id: 6,
                name: '星空图案',
                colorType: '双色',
                rowCount: 6,
                actualHeight: 55.0,
                bleedHeight: 75.0,
                customerSquare: 128
            },
            {
                id: 7,
                name: '叶子纹理',
                colorType: '单色',
                rowCount: 9,
                actualHeight: 48.0,
                bleedHeight: 68.0,
                customerSquare: 211
            },
            {
                id: 8,
                name: '抽象线条',
                colorType: '双色',
                rowCount: 7,
                actualHeight: 52.0,
                bleedHeight: 72.0,
                customerSquare: 155
            },
            {
                id: 9,
                name: '水波纹',
                colorType: '单色',
                rowCount: 11,
                actualHeight: 42.0,
                bleedHeight: 62.0,
                customerSquare: 283
            },
            {
                id: 10,
                name: '云朵图案',
                colorType: '三色',
                rowCount: 14,
                actualHeight: 38.0,
                bleedHeight: 58.0,
                customerSquare: 386
            },
            {
                id: 11,
                name: '格子纹理',
                colorType: '双色',
                rowCount: 5,
                actualHeight: 58.0,
                bleedHeight: 78.0,
                customerSquare: 102
            },
            {
                id: 12,
                name: '樱花图案',
                colorType: '单色',
                rowCount: 12,
                actualHeight: 46.0,
                bleedHeight: 66.0,
                customerSquare: 290
            }
        ];

        // 打印耗材数据
        let materials = [
            {
                id: 1,
                name: '白墨打印纸',
                category: '打印纸张',
                unit: '张',
                price: 0.5,
                stock: 5000,
                supplier: '纸张供应商A',
                status: '正常'
            },
            {
                id: 2,
                name: '黑色墨水',
                category: '墨水耗材',
                unit: '瓶',
                price: 120.0,
                stock: 8,
                supplier: '墨水供应商B',
                status: '库存不足'
            },
            {
                id: 3,
                name: '清洗液',
                category: '清洗用品',
                unit: '瓶',
                price: 45.0,
                stock: 30,
                supplier: '清洗用品公司',
                status: '正常'
            }
        ];

        let editingOrderId = null;
        let editingCustomerId = null;
        let editingPatternId = null;
        let editingMaterialId = null;

        // 页面切换
        function showPage(pageId, clickedElement = null) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // 显示目标页面
            document.getElementById(pageId).classList.add('active');

            // 更新菜单状态
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });

            // 设置对应菜单项为active
            if (clickedElement) {
                clickedElement.classList.add('active');
            } else {
                // 如果没有传入点击元素，根据pageId找到对应的菜单项
                const menuItem = document.querySelector(`[onclick="showPage('${pageId}', this)"]`);
                if (menuItem) {
                    menuItem.classList.add('active');
                }
            }

            // 根据页面类型刷新对应表格
            if (pageId === 'orders') {
                renderOrdersTable();
                loadCustomerOptions();
                loadPatternOptions();
            } else if (pageId === 'customers') {
                renderCustomersTable();
            } else if (pageId === 'patterns') {
                renderPatternsTable();
            } else if (pageId === 'materials') {
                renderMaterialsTable();
            } else if (pageId === 'dashboard') {
                updateDashboard();
            } else if (pageId === 'finance') {
                updateFinanceData();
            }
        }

        // 打印计算公式
        function calculatePrintData(rowCount, actualHeight) {
            const cleanHeight = actualHeight + 20; // 清洗出品 = 实际高度 + 20mm
            const customerSquare = 1000 / cleanHeight / rowCount; // 客户方数量 = 1000 / 清洗出品 / 行数量
            return {
                cleanHeight: cleanHeight,
                customerSquare: customerSquare
            };
        }

        // 切换计费方式
        function changeBillingType() {
            const billingType = document.querySelector('input[name="billingType"]:checked').value;
            const piecesGroup = document.getElementById('piecesGroup');
            const squareGroup = document.getElementById('squareGroup');

            if (billingType === 'pieces') {
                piecesGroup.style.display = 'block';
                squareGroup.style.display = 'none';
                document.getElementById('orderSquareCount').value = 0;
            } else {
                piecesGroup.style.display = 'none';
                squareGroup.style.display = 'block';
                document.getElementById('orderPiecesCount').value = 0;
            }

            calculateOrderPrice();
        }

        // 计算订单价格
        function calculateOrderPrice() {
            const customerName = document.getElementById('orderCustomerName').value;
            const patternName = patternSelector ? patternSelector.getValue() : '';
            const billingType = document.querySelector('input[name="billingType"]:checked')?.value;
            const piecesCount = parseInt(document.getElementById('orderPiecesCount').value) || 0;
            const squareCount = parseFloat(document.getElementById('orderSquareCount').value) || 0;

            if (!customerName || !patternName || !billingType) {
                document.getElementById('orderUnitPrice').value = '';
                document.getElementById('orderTotalPrice').value = '';
                return;
            }

            const customer = customers.find(c => c.name === customerName);
            const pattern = patterns.find(p => p.name === patternName);

            if (!customer || !pattern) {
                return;
            }

            let unitPrice = 0;
            let totalPrice = 0;

            if (billingType === 'pieces') {
                // 个数单价 = 客户单价 ÷ 每平方数量
                unitPrice = customer.unitPrice / pattern.customerSquare;
                totalPrice = unitPrice * piecesCount;
            } else if (billingType === 'square') {
                // 平方单价 = 16元/平方
                unitPrice = 16;
                totalPrice = unitPrice * squareCount;
            }

            document.getElementById('orderUnitPrice').value = unitPrice.toFixed(2);
            document.getElementById('orderTotalPrice').value = totalPrice.toFixed(2);
        }

        // 计算图案数据
        function calculatePatternData() {
            const rowCount = parseInt(document.getElementById('patternRowCount').value) || 0;
            const actualHeight = parseFloat(document.getElementById('patternActualHeight').value) || 0;

            if (actualHeight) {
                // 添加出血 = 实际高度 + 20mm
                const bleedHeight = actualHeight + 20;
                document.getElementById('patternBleedHeight').value = bleedHeight.toFixed(1);

                if (rowCount) {
                    // 每平方数量 = (1600mm ÷ 添加出血) × 行数量 (取整数，直接舍去小数)
                    const customerSquare = Math.floor((1600 / bleedHeight) * rowCount);
                    document.getElementById('patternCustomerSquare').value = customerSquare;
                } else {
                    document.getElementById('patternCustomerSquare').value = '';
                }
            } else {
                document.getElementById('patternBleedHeight').value = '';
                document.getElementById('patternCustomerSquare').value = '';
            }
        }

        // 加载客户选项
        function loadCustomerOptions() {
            const select = document.getElementById('orderCustomerName');
            select.innerHTML = '<option value="">请选择客户</option>';
            customers.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.name;
                option.textContent = `${customer.name} (¥${customer.unitPrice}/单位)`;
                select.appendChild(option);
            });
        }

        // 可搜索下拉框类
        class SearchableSelect {
            constructor(containerId, options = {}) {
                this.container = document.getElementById(containerId);
                this.input = this.container.querySelector('.searchable-select-input');
                this.dropdown = this.container.querySelector('.searchable-select-dropdown');
                this.arrow = this.container.querySelector('.searchable-select-arrow');

                this.data = [];
                this.filteredData = [];
                this.selectedValue = '';
                this.selectedText = '';
                this.placeholder = options.placeholder || '请选择...';
                this.onSelect = options.onSelect || (() => {});

                this.init();
            }

            init() {
                // 点击输入框显示/隐藏下拉框
                this.input.addEventListener('click', () => {
                    this.toggle();
                });

                // 输入搜索
                this.input.addEventListener('input', (e) => {
                    this.search(e.target.value);
                });

                // 点击外部关闭下拉框
                document.addEventListener('click', (e) => {
                    if (!this.container.contains(e.target)) {
                        this.close();
                    }
                });

                // 键盘导航
                this.input.addEventListener('keydown', (e) => {
                    this.handleKeydown(e);
                });
            }

            setData(data) {
                this.data = data;
                this.filteredData = [...data];
                this.renderOptions();
            }

            search(query) {
                if (!query) {
                    this.filteredData = [...this.data];
                } else {
                    this.filteredData = this.data.filter(item =>
                        item.text.toLowerCase().includes(query.toLowerCase()) ||
                        item.value.toLowerCase().includes(query.toLowerCase())
                    );
                }
                this.renderOptions();
                this.open();
            }

            renderOptions() {
                this.dropdown.innerHTML = '';

                if (this.filteredData.length === 0) {
                    const noResults = document.createElement('div');
                    noResults.className = 'searchable-select-no-results';
                    noResults.textContent = '没有找到匹配的图案';
                    this.dropdown.appendChild(noResults);
                    return;
                }

                this.filteredData.forEach(item => {
                    const option = document.createElement('div');
                    option.className = 'searchable-select-option';
                    if (item.value === this.selectedValue) {
                        option.classList.add('selected');
                    }
                    option.textContent = item.text;
                    option.dataset.value = item.value;

                    option.addEventListener('click', () => {
                        this.selectOption(item);
                    });

                    this.dropdown.appendChild(option);
                });
            }

            selectOption(item) {
                this.selectedValue = item.value;
                this.selectedText = item.text;
                this.input.value = item.text;
                this.input.setAttribute('data-value', item.value);
                this.close();
                this.onSelect(item);
            }

            open() {
                this.container.classList.add('open');
                this.input.removeAttribute('readonly');
            }

            close() {
                this.container.classList.remove('open');
                if (this.selectedValue) {
                    this.input.value = this.selectedText;
                } else {
                    this.input.value = '';
                }
                this.input.setAttribute('readonly', 'true');
            }

            toggle() {
                if (this.container.classList.contains('open')) {
                    this.close();
                } else {
                    this.open();
                }
            }

            handleKeydown(e) {
                if (e.key === 'Escape') {
                    this.close();
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    const selected = this.dropdown.querySelector('.searchable-select-option.selected');
                    if (selected) {
                        const value = selected.dataset.value;
                        const item = this.filteredData.find(item => item.value === value);
                        if (item) {
                            this.selectOption(item);
                        }
                    }
                }
            }

            getValue() {
                return this.selectedValue;
            }

            setValue(value) {
                const item = this.data.find(item => item.value === value);
                if (item) {
                    this.selectOption(item);
                } else {
                    this.selectedValue = '';
                    this.selectedText = '';
                    this.input.value = '';
                    this.input.removeAttribute('data-value');
                }
            }

            clear() {
                this.selectedValue = '';
                this.selectedText = '';
                this.input.value = '';
                this.input.removeAttribute('data-value');
            }
        }

        // 图案选择器实例
        let patternSelector = null;

        // 加载图案选项
        function loadPatternOptions() {
            if (!patternSelector) {
                patternSelector = new SearchableSelect('orderPatternSelect', {
                    placeholder: '搜索或选择图案...',
                    onSelect: (item) => {
                        loadPatternData();
                    }
                });
            }

            const patternData = patterns.map(pattern => ({
                value: pattern.name,
                text: `${pattern.name} (${pattern.colorType})`
            }));

            patternSelector.setData(patternData);
        }

        // 加载图案数据
        function loadPatternData() {
            const patternName = patternSelector ? patternSelector.getValue() : '';
            if (patternName) {
                const pattern = patterns.find(p => p.name === patternName);
                if (pattern) {
                    document.getElementById('orderColorType').value = pattern.colorType;
                    document.getElementById('orderRowCount').value = pattern.rowCount;
                    document.getElementById('orderActualHeight').value = pattern.actualHeight;
                    document.getElementById('orderBleedHeight').value = pattern.bleedHeight;
                    document.getElementById('orderCustomerSquare').value = pattern.customerSquare;

                    // 重新计算价格
                    calculateOrderPrice();
                }
            } else {
                // 清空数据
                document.getElementById('orderColorType').value = '';
                document.getElementById('orderRowCount').value = '';
                document.getElementById('orderActualHeight').value = '';
                document.getElementById('orderBleedHeight').value = '';
                document.getElementById('orderCustomerSquare').value = '';
                document.getElementById('orderTotalPrice').value = '';
            }
        }

        // 更新仪表板数据
        function updateDashboard() {
            const today = new Date().toISOString().split('T')[0];
            const thisMonth = new Date().toISOString().slice(0, 7);

            // 计算今日订单和收入
            const todayOrders = orders.filter(order => order.createTime.startsWith(today));
            const todayIncome = todayOrders.reduce((sum, order) => sum + order.totalPrice, 0);

            // 计算本月订单和收入
            const monthOrders = orders.filter(order => order.createTime.startsWith(thisMonth));
            const monthIncome = monthOrders.reduce((sum, order) => sum + order.totalPrice, 0);

            // 更新仪表板显示
            document.getElementById('todayOrders').textContent = todayOrders.length;
            document.getElementById('todayIncome').textContent = `¥${todayIncome.toFixed(2)}`;
            document.getElementById('monthOrders').textContent = monthOrders.length;
            document.getElementById('monthIncome').textContent = `¥${monthIncome.toFixed(2)}`;

            // 更新最近订单
            const recentOrders = orders.slice(-4).reverse();
            const recentOrdersHtml = recentOrders.map(order => `
                <div style="padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                    <span style="color: #1890ff;">${order.customerName}</span> ${order.colorType}
                    <span style="color: #999;">${order.createTime.split(' ')[1]}</span>
                </div>
            `).join('');
            document.getElementById('recentOrders').innerHTML = recentOrdersHtml;
        }

        // 更新财务数据
        function updateFinanceData() {
            const today = new Date().toISOString().split('T')[0];
            const thisMonth = new Date().toISOString().slice(0, 7);
            const thisYear = new Date().getFullYear().toString();

            // 计算收入
            const todayIncome = orders.filter(order => order.createTime.startsWith(today))
                                   .reduce((sum, order) => sum + order.totalPrice, 0);
            const monthIncome = orders.filter(order => order.createTime.startsWith(thisMonth))
                                   .reduce((sum, order) => sum + order.totalPrice, 0);
            const yearIncome = orders.filter(order => order.createTime.startsWith(thisYear))
                                  .reduce((sum, order) => sum + order.totalPrice, 0);

            document.getElementById('financeToday').textContent = `¥${todayIncome.toFixed(2)}`;
            document.getElementById('financeMonth').textContent = `¥${monthIncome.toFixed(2)}`;
            document.getElementById('financeYear').textContent = `¥${yearIncome.toFixed(2)}`;
        }

        // 渲染订单表格
        function renderOrdersTable() {
            const tbody = document.getElementById('ordersTableBody');
            if (!tbody) {
                console.error('订单表格元素未找到');
                return;
            }

            const searchInput = document.getElementById('orderSearch');
            const colorFilterSelect = document.getElementById('colorFilter');

            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const colorFilter = colorFilterSelect ? colorFilterSelect.value : '';

            console.log('渲染订单表格，订单数量：', orders.length);

            let filteredOrders = orders.filter(order => {
                const matchesSearch = order.customerName.toLowerCase().includes(searchTerm);
                const matchesColor = !colorFilter || order.colorType === colorFilter;
                return matchesSearch && matchesColor;
            });

            console.log('过滤后订单数量：', filteredOrders.length);

            if (filteredOrders.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; color: #999;">暂无订单数据</td></tr>';
                return;
            }

            tbody.innerHTML = filteredOrders.map(order => `
                <tr>
                    <td>${order.customerName || '-'}</td>
                    <td>${order.patternName || '-'}</td>
                    <td>${order.colorType || '-'}</td>
                    <td>${order.billingType === 'pieces' ? '按个数' : '按平方'}</td>
                    <td>${order.billingType === 'pieces' ? (order.piecesCount || 0) + '个' : (order.squareCount || 0) + '㎡'}</td>
                    <td>¥${(order.unitPrice || 0).toFixed(2)}</td>
                    <td>¥${(order.totalPrice || 0).toFixed(2)}</td>
                    <td>${order.createTime || '-'}</td>
                    <td>
                        <button class="btn" style="padding: 4px 8px; margin-right: 4px;" onclick="editOrder(${order.id})">编辑</button>
                        <button class="btn btn-danger" style="padding: 4px 8px;" onclick="deleteOrder(${order.id})">删除</button>
                    </td>
                </tr>
            `).join('');
        }

        // 订单管理功能
        function showOrderModal(orderId = null) {
            editingOrderId = orderId;
            const modal = document.getElementById('orderModal');
            const title = document.getElementById('orderModalTitle');
            const form = document.getElementById('orderForm');

            loadCustomerOptions();
            loadPatternOptions();

            if (orderId) {
                const order = orders.find(o => o.id === orderId);
                title.textContent = '编辑订单';
                document.getElementById('orderCustomerName').value = order.customerName;

                // 设置图案选择
                if (patternSelector) {
                    patternSelector.setValue(order.patternName);
                }

                // 设置计费方式
                document.querySelector(`input[name="billingType"][value="${order.billingType}"]`).checked = true;
                changeBillingType();

                document.getElementById('orderColorType').value = order.colorType;
                document.getElementById('orderRowCount').value = order.rowCount;
                document.getElementById('orderActualHeight').value = order.actualHeight;
                document.getElementById('orderBleedHeight').value = order.bleedHeight;
                document.getElementById('orderCustomerSquare').value = order.customerSquare;
                document.getElementById('orderPiecesCount').value = order.piecesCount;
                document.getElementById('orderSquareCount').value = order.squareCount;
                document.getElementById('orderUnitPrice').value = order.unitPrice;
                document.getElementById('orderTotalPrice').value = order.totalPrice;

                // 设置订单日期
                if (order.createTime) {
                    const orderDate = order.createTime.split(' ')[0]; // 提取日期部分
                    document.getElementById('orderDate').value = orderDate;
                } else {
                    const today = new Date().toISOString().split('T')[0];
                    document.getElementById('orderDate').value = today;
                }
            } else {
                title.textContent = '新建订单';
                form.reset();
                // 清空图案选择
                if (patternSelector) {
                    patternSelector.clear();
                }
                // 设置默认日期为今天
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('orderDate').value = today;
                // 默认选择按个数计费
                document.querySelector('input[name="billingType"][value="pieces"]').checked = true;
                changeBillingType();
            }

            modal.classList.add('active');
        }

        function closeOrderModal() {
            document.getElementById('orderModal').classList.remove('active');
            editingOrderId = null;
        }

        function editOrder(id) {
            showOrderModal(id);
        }

        function deleteOrder(id) {
            if (confirm('确定要删除这个订单吗？')) {
                // 找到要删除的订单
                const order = orders.find(o => o.id === id);
                if (order) {
                    // 恢复客户余额
                    const customer = customers.find(c => c.name === order.customerName);
                    if (customer) {
                        customer.balance = (customer.balance || 0) + order.totalPrice;

                        // 添加退款记录
                        const record = {
                            id: Date.now() + Math.random(),
                            customerId: customer.id,
                            customerName: customer.name,
                            amount: order.totalPrice, // 正数表示退款
                            type: 'refund',
                            description: `订单删除退款 - ${order.patternName}`,
                            createTime: new Date().toISOString().replace('T', ' ').slice(0, 19)
                        };
                        rechargeRecords.push(record);
                    }
                }

                orders = orders.filter(o => o.id !== id);
                renderOrdersTable();
                // 更新客户表格显示余额变化
                if (isCardView) {
                    renderCustomersCards();
                } else {
                    renderCustomersTable();
                }

                // 如果当前在客户详情页面，更新详情信息和订单列表
                if (currentCustomerId && customer && customer.id === currentCustomerId) {
                    renderCustomerDetailInfo();
                    renderCustomerOrders();
                }

                saveToStorage();
                updateDashboard();
            }
        }

        // 客户管理功能
        let isCardView = true; // 默认卡片视图

        // 切换视图模式
        function toggleCustomerView() {
            isCardView = !isCardView;
            const cardsContainer = document.getElementById('customersCards');
            const tableContainer = document.getElementById('customersTableContainer');
            const toggleText = document.getElementById('viewToggleText');

            if (isCardView) {
                cardsContainer.style.display = 'grid';
                tableContainer.style.display = 'none';
                toggleText.textContent = '📋 表格视图';
                renderCustomersCards();
            } else {
                cardsContainer.style.display = 'none';
                tableContainer.style.display = 'block';
                toggleText.textContent = '🎴 卡片视图';
                renderCustomersTable();
            }
        }

        // 更新客户统计
        function updateCustomerStats() {
            const totalCustomersEl = document.getElementById('totalCustomers');
            const totalBalanceEl = document.getElementById('totalBalance');
            const totalDebtEl = document.getElementById('totalDebt');
            const avgPriceEl = document.getElementById('avgPrice');

            if (!totalCustomersEl) return;

            const totalCustomers = customers.length;
            let totalBalance = 0;
            let totalDebt = 0;
            let totalPrice = 0;

            customers.forEach(customer => {
                const balance = customer.balance || 0;
                if (balance >= 0) {
                    totalBalance += balance;
                } else {
                    totalDebt += Math.abs(balance);
                }
                totalPrice += customer.unitPrice || 0;
            });

            const avgPrice = totalCustomers > 0 ? totalPrice / totalCustomers : 0;

            totalCustomersEl.textContent = totalCustomers;
            totalBalanceEl.textContent = `¥${totalBalance.toFixed(2)}`;
            totalDebtEl.textContent = `¥${totalDebt.toFixed(2)}`;
            avgPriceEl.textContent = `¥${avgPrice.toFixed(2)}`;
        }

        // 渲染客户卡片
        function renderCustomersCards() {
            const container = document.getElementById('customersCards');
            const searchTerm = document.getElementById('customerSearch').value.toLowerCase();
            const balanceFilter = document.getElementById('customerBalanceFilter')?.value || 'all';
            const sortBy = document.getElementById('customerSortBy')?.value || 'name';

            if (!container) return;

            // 筛选客户
            let filteredCustomers = customers.filter(customer => {
                const matchesSearch = customer.name.toLowerCase().includes(searchTerm);

                const balance = customer.balance || 0;
                let matchesFilter = true;

                if (balanceFilter === 'debt') matchesFilter = balance < 0;
                else if (balanceFilter === 'low') matchesFilter = balance >= 0 && balance < 100;
                else if (balanceFilter === 'normal') matchesFilter = balance >= 100 && balance < 300;
                else if (balanceFilter === 'good') matchesFilter = balance >= 300;

                return matchesSearch && matchesFilter;
            });

            // 排序客户
            filteredCustomers.sort((a, b) => {
                switch (sortBy) {
                    case 'balance':
                        return (b.balance || 0) - (a.balance || 0);
                    case 'price':
                        return (b.unitPrice || 0) - (a.unitPrice || 0);
                    case 'recent':
                        const aLastOrder = getLastOrderTime(a.name);
                        const bLastOrder = getLastOrderTime(b.name);
                        return new Date(bLastOrder) - new Date(aLastOrder);
                    default: // name
                        return a.name.localeCompare(b.name);
                }
            });

            container.innerHTML = filteredCustomers.map(customer => {
                const balance = customer.balance || 0;
                let balanceText, balanceColor, statusText, statusIcon;

                if (balance < 0) {
                    balanceText = `欠款 ¥${Math.abs(balance).toFixed(2)}`;
                    balanceColor = '#ff4d4f';
                    statusText = '欠款';
                    statusIcon = '🚨';
                } else if (balance < 100) {
                    balanceText = `¥${balance.toFixed(2)}`;
                    balanceColor = '#ff4d4f';
                    statusText = '余额不足';
                    statusIcon = '⚠️';
                } else if (balance < 300) {
                    balanceText = `¥${balance.toFixed(2)}`;
                    balanceColor = '#faad14';
                    statusText = '余额偏低';
                    statusIcon = '⚡';
                } else {
                    balanceText = `¥${balance.toFixed(2)}`;
                    balanceColor = '#52c41a';
                    statusText = '余额充足';
                    statusIcon = '✅';
                }

                // 获取客户订单统计
                const customerOrders = orders.filter(order => order.customerName === customer.name);
                const orderCount = customerOrders.length;
                const totalSpent = customerOrders.reduce((sum, order) => sum + order.totalPrice, 0);
                const lastOrderTime = getLastOrderTime(customer.name);

                return `
                    <div style="background: white; padding: 20px; border-radius: 12px; border-left: 4px solid ${balanceColor}; box-shadow: 0 4px 6px rgba(0,0,0,0.1); transition: all 0.3s;" onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 12px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'">
                        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 16px;">
                            <div>
                                <h3 style="margin: 0 0 8px 0; color: #333; font-size: 18px;">${customer.name}</h3>
                                <div style="display: flex; align-items: center; gap: 6px; margin-bottom: 4px;">
                                    <span>${statusIcon}</span>
                                    <span style="color: ${balanceColor}; font-weight: bold; font-size: 14px;">${statusText}</span>
                                </div>
                                <div style="font-size: 12px; color: #999;">客户ID: ${customer.id}</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="color: ${balanceColor}; font-size: 20px; font-weight: bold; margin-bottom: 4px;">${balanceText}</div>
                                <div style="font-size: 12px; color: #666;">账户余额</div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 16px; padding: 12px; background: #f8f9fa; border-radius: 6px;">
                            <div style="text-align: center;">
                                <div style="font-size: 16px; font-weight: bold; color: #1890ff;">¥${customer.unitPrice.toFixed(2)}</div>
                                <div style="font-size: 12px; color: #666;">单价/个</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 16px; font-weight: bold; color: #722ed1;">${orderCount}</div>
                                <div style="font-size: 12px; color: #666;">历史订单</div>
                            </div>
                        </div>

                        <div style="margin-bottom: 16px; padding: 8px; background: #f0f0f0; border-radius: 4px; font-size: 12px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                <span>累计消费:</span>
                                <span style="font-weight: bold; color: #52c41a;">¥${totalSpent.toFixed(2)}</span>
                            </div>
                            <div style="display: flex; justify-content: space-between;">
                                <span>最近订单:</span>
                                <span style="color: #666;">${lastOrderTime || '暂无订单'}</span>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 8px;">
                            <button class="btn" style="padding: 8px 4px; font-size: 12px; background: #1890ff; color: white; border-radius: 4px;" onclick="editCustomer(${customer.id})" title="编辑客户">✏️</button>
                            <button class="btn" style="padding: 8px 4px; font-size: 12px; background: #52c41a; color: white; border-radius: 4px;" onclick="quickRecharge(${customer.id})" title="客户充值">💰</button>
                            <button class="btn" style="padding: 8px 4px; font-size: 12px; background: #ff4d4f; color: white; border-radius: 4px;" onclick="addDebt(${customer.id})" title="增加欠款">📝</button>
                            <button class="btn" style="padding: 8px 4px; font-size: 12px; background: #fa8c16; color: white; border-radius: 4px;" onclick="showCustomerDetailPage(${customer.id})" title="查看详情">👁️</button>
                        </div>
                    </div>
                `;
            }).join('');

            updateCustomerStats();
        }

        // 获取客户最后订单时间
        function getLastOrderTime(customerName) {
            const customerOrders = orders.filter(order => order.customerName === customerName);
            if (customerOrders.length === 0) return null;

            const lastOrder = customerOrders.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))[0];
            return lastOrder.createTime.split(' ')[0]; // 只返回日期部分
        }

        // 当前查看的客户ID
        let currentCustomerId = null;

        // 显示客户详情页面
        function showCustomerDetailPage(customerId) {
            currentCustomerId = customerId;
            const customer = customers.find(c => c.id === customerId);
            if (!customer) {
                alert('客户不存在！');
                return;
            }

            // 更新页面标题
            document.getElementById('customerDetailTitle').textContent = `${customer.name} - 客户详情`;
            document.getElementById('customerDetailDescription').textContent = `查看 ${customer.name} 的完整信息和订单记录`;

            // 显示客户详情页面
            showPage('customerDetail');

            // 渲染客户详情
            renderCustomerDetailInfo();
            renderCustomerOrders();
        }

        // 渲染客户详情信息
        function renderCustomerDetailInfo() {
            if (!currentCustomerId) return;

            const customer = customers.find(c => c.id === currentCustomerId);
            if (!customer) return;

            const customerOrders = orders.filter(order => order.customerName === customer.name);
            const balance = customer.balance || 0;

            // 基本信息
            document.getElementById('customerDetailName').textContent = customer.name;
            document.getElementById('customerDetailPrice').textContent = `¥${customer.unitPrice.toFixed(2)}`;
            document.getElementById('customerDetailTotalOrders').textContent = customerOrders.length;

            // 余额显示
            const balanceEl = document.getElementById('customerDetailBalance');
            if (balance < 0) {
                balanceEl.textContent = `欠款 ¥${Math.abs(balance).toFixed(2)}`;
                balanceEl.style.color = '#ff4d4f';
            } else {
                balanceEl.textContent = `¥${balance.toFixed(2)}`;
                balanceEl.style.color = balance < 100 ? '#ff4d4f' : balance < 300 ? '#faad14' : '#52c41a';
            }

            // 计算财务统计
            const today = new Date().toISOString().split('T')[0];
            const currentMonth = new Date().toISOString().slice(0, 7);

            let todayTotal = 0, todayCount = 0;
            let monthTotal = 0, monthCount = 0;
            let totalSpent = 0;

            customerOrders.forEach(order => {
                const orderDate = order.createTime.split(' ')[0];
                const orderMonth = orderDate.slice(0, 7);

                totalSpent += order.totalPrice;

                if (orderDate === today) {
                    todayTotal += order.totalPrice;
                    todayCount++;
                }

                if (orderMonth === currentMonth) {
                    monthTotal += order.totalPrice;
                    monthCount++;
                }
            });

            const avgOrder = customerOrders.length > 0 ? totalSpent / customerOrders.length : 0;

            // 更新财务统计显示
            document.getElementById('customerTodayTotal').textContent = `¥${todayTotal.toFixed(2)}`;
            document.getElementById('customerTodayCount').textContent = `今日订单: ${todayCount} 个`;
            document.getElementById('customerTotalSpent').textContent = `¥${totalSpent.toFixed(2)}`;
            document.getElementById('customerAvgOrder').textContent = `平均订单: ¥${avgOrder.toFixed(2)}`;
            document.getElementById('customerMonthTotal').textContent = `¥${monthTotal.toFixed(2)}`;
            document.getElementById('customerMonthCount').textContent = `本月订单: ${monthCount} 个`;
        }

        // 渲染客户订单记录
        function renderCustomerOrders() {
            if (!currentCustomerId) return;

            const customer = customers.find(c => c.id === currentCustomerId);
            if (!customer) return;

            const tbody = document.getElementById('customerOrdersTableBody');
            const filterValue = document.getElementById('customerOrderFilter')?.value || 'all';
            const searchTerm = document.getElementById('customerOrderSearch')?.value.toLowerCase() || '';

            // 获取客户订单
            let customerOrders = orders.filter(order => order.customerName === customer.name);

            // 按时间筛选
            const today = new Date().toISOString().split('T')[0];
            const currentMonth = new Date().toISOString().slice(0, 7);
            const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

            if (filterValue === 'today') {
                customerOrders = customerOrders.filter(order => order.createTime.split(' ')[0] === today);
            } else if (filterValue === 'week') {
                customerOrders = customerOrders.filter(order => order.createTime.split(' ')[0] >= oneWeekAgo);
            } else if (filterValue === 'month') {
                customerOrders = customerOrders.filter(order => order.createTime.split(' ')[0].slice(0, 7) === currentMonth);
            }

            // 按图案名称搜索
            if (searchTerm) {
                customerOrders = customerOrders.filter(order =>
                    order.patternName.toLowerCase().includes(searchTerm)
                );
            }

            // 按时间倒序排列
            customerOrders.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));

            tbody.innerHTML = customerOrders.map(order => {
                const billingText = order.billingType === 'pieces' ? '按个数' : '按平方';
                const quantity = order.billingType === 'pieces' ? `${order.piecesCount}个` : `${order.squareCount}㎡`;

                return `
                    <tr>
                        <td style="font-size: 12px;">${order.createTime}</td>
                        <td style="font-weight: bold;">${order.patternName}</td>
                        <td><span style="padding: 2px 6px; background: #f0f0f0; border-radius: 3px; font-size: 12px;">${order.colorType}</span></td>
                        <td style="font-size: 12px;">${billingText}</td>
                        <td style="font-weight: bold; color: #1890ff;">${quantity}</td>
                        <td style="font-weight: bold; color: #722ed1;">${order.customerSquare || 0}</td>
                        <td style="color: #52c41a;">¥${order.unitPrice.toFixed(2)}</td>
                        <td style="font-weight: bold; font-size: 16px; color: #ff4d4f;">¥${order.totalPrice.toFixed(2)}</td>
                        <td>
                            <button class="btn" style="padding: 4px 8px; font-size: 12px; margin-right: 4px;" onclick="editOrder(${order.id})">编辑</button>
                            <button class="btn btn-danger" style="padding: 4px 8px; font-size: 12px;" onclick="deleteOrder(${order.id})">删除</button>
                        </td>
                    </tr>
                `;
            }).join('');

            // 如果没有订单，显示提示
            if (customerOrders.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 40px; color: #999;">
                            <div style="font-size: 16px; margin-bottom: 8px;">📋</div>
                            <div>暂无符合条件的订单记录</div>
                        </td>
                    </tr>
                `;
            }
        }

        // 查看客户详情（原有的模态框方式，保留兼容性）
        function viewCustomerDetails(customerId) {
            const customer = customers.find(c => c.id === customerId);
            if (!customer) return;

            const customerOrders = orders.filter(order => order.customerName === customer.name);
            const totalSpent = customerOrders.reduce((sum, order) => sum + order.totalPrice, 0);
            const avgOrderValue = customerOrders.length > 0 ? totalSpent / customerOrders.length : 0;

            const recentOrders = customerOrders
                .sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
                .slice(0, 5);

            let detailsHtml = `
                <div style="max-height: 500px; overflow-y: auto;">
                    <h4 style="margin-bottom: 16px; color: #333;">客户详细信息</h4>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px;">
                        <div style="background: #f8f9fa; padding: 12px; border-radius: 6px;">
                            <strong>基本信息</strong><br>
                            客户姓名: ${customer.name}<br>
                            客户单价: ¥${customer.unitPrice.toFixed(2)}/个<br>
                            账户余额: ¥${(customer.balance || 0).toFixed(2)}
                        </div>
                        <div style="background: #f8f9fa; padding: 12px; border-radius: 6px;">
                            <strong>消费统计</strong><br>
                            订单总数: ${customerOrders.length} 个<br>
                            累计消费: ¥${totalSpent.toFixed(2)}<br>
                            平均订单: ¥${avgOrderValue.toFixed(2)}
                        </div>
                    </div>

                    <h5 style="margin-bottom: 12px;">最近订单记录</h5>
                    <div style="max-height: 200px; overflow-y: auto;">
            `;

            if (recentOrders.length > 0) {
                detailsHtml += `
                    <table style="width: 100%; font-size: 12px; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f0f0f0;">
                                <th style="padding: 6px; border: 1px solid #ddd;">时间</th>
                                <th style="padding: 6px; border: 1px solid #ddd;">图案</th>
                                <th style="padding: 6px; border: 1px solid #ddd;">数量</th>
                                <th style="padding: 6px; border: 1px solid #ddd;">金额</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                recentOrders.forEach(order => {
                    const quantity = order.billingType === 'pieces' ? `${order.piecesCount}个` : `${order.squareCount}㎡`;
                    detailsHtml += `
                        <tr>
                            <td style="padding: 6px; border: 1px solid #ddd;">${order.createTime.split(' ')[0]}</td>
                            <td style="padding: 6px; border: 1px solid #ddd;">${order.patternName}</td>
                            <td style="padding: 6px; border: 1px solid #ddd;">${quantity}</td>
                            <td style="padding: 6px; border: 1px solid #ddd;">¥${order.totalPrice.toFixed(2)}</td>
                        </tr>
                    `;
                });

                detailsHtml += '</tbody></table>';
            } else {
                detailsHtml += '<p style="color: #999; text-align: center; padding: 20px;">暂无订单记录</p>';
            }

            detailsHtml += '</div></div>';

            // 显示在模态框中
            const modal = document.getElementById('financeReportModal');
            const title = document.getElementById('financeReportModalTitle');
            const content = document.getElementById('financeReportContent');

            title.textContent = `${customer.name} - 客户详情`;
            content.innerHTML = detailsHtml;
            modal.classList.add('active');
        }

        // 客户详情页面的快捷操作函数
        function quickRechargeFromDetail() {
            if (currentCustomerId) {
                quickRecharge(currentCustomerId);
            }
        }

        function addDebtFromDetail() {
            if (currentCustomerId) {
                addDebt(currentCustomerId);
            }
        }

        function editCustomerFromDetail() {
            if (currentCustomerId) {
                editCustomer(currentCustomerId);
            }
        }

        function preSelectCustomer() {
            if (currentCustomerId) {
                const customer = customers.find(c => c.id === currentCustomerId);
                if (customer) {
                    // 延迟设置，确保订单模态框已经打开
                    setTimeout(() => {
                        const customerSelect = document.getElementById('orderCustomerName');
                        if (customerSelect) {
                            customerSelect.value = customer.name;
                        }
                    }, 100);
                }
            }
        }

        // 导出客户报表
        function exportCustomerReport() {
            if (!currentCustomerId) return;

            const customer = customers.find(c => c.id === currentCustomerId);
            if (!customer) return;

            const customerOrders = orders.filter(order => order.customerName === customer.name);

            const reportData = [
                [`${customer.name} - 客户报表`, '', '', '', '', '', '', ''],
                ['导出时间：' + new Date().toLocaleString(), '', '', '', '', '', '', ''],
                ['', '', '', '', '', '', '', ''],
                ['客户基本信息', '', '', '', '', '', '', ''],
                ['客户姓名', customer.name, '客户单价', `¥${customer.unitPrice.toFixed(2)}`, '账户余额', `¥${(customer.balance || 0).toFixed(2)}`, '历史订单', `${customerOrders.length}个`],
                ['', '', '', '', '', '', '', ''],
                ['订单明细', '', '', '', '', '', '', ''],
                ['订单时间', '图案名称', '色号', '计费方式', '数量', '每平方数量', '图案单价', '订单金额']
            ];

            let totalAmount = 0;
            customerOrders.sort((a, b) => new Date(b.createTime) - new Date(a.createTime)).forEach(order => {
                totalAmount += order.totalPrice;
                const billingText = order.billingType === 'pieces' ? '按个数' : '按平方';
                const quantity = order.billingType === 'pieces' ? `${order.piecesCount}个` : `${order.squareCount}㎡`;

                reportData.push([
                    order.createTime,
                    order.patternName,
                    order.colorType,
                    billingText,
                    quantity,
                    order.customerSquare || 0,
                    order.unitPrice.toFixed(2),
                    order.totalPrice.toFixed(2)
                ]);
            });

            reportData.push(['', '', '', '', '', '', '累计消费：', totalAmount.toFixed(2)]);

            downloadExcel(reportData, `${customer.name}_客户报表_${new Date().toISOString().slice(0, 10)}.xlsx`);
        }

        // 表格视图渲染
        function renderCustomersTable() {
            const tbody = document.getElementById('customersTableBody');
            const searchTerm = document.getElementById('customerSearch').value.toLowerCase();

            let filteredCustomers = customers.filter(customer => {
                return customer.name.toLowerCase().includes(searchTerm);
            });

            tbody.innerHTML = filteredCustomers.map(customer => {
                const balance = customer.balance || 0;
                let balanceText, balanceColor;

                if (balance < 0) {
                    balanceText = `欠款 ¥${Math.abs(balance).toFixed(2)}`;
                    balanceColor = '#ff4d4f';
                } else if (balance < 100) {
                    balanceText = `¥${balance.toFixed(2)}`;
                    balanceColor = '#ff4d4f';
                } else if (balance < 300) {
                    balanceText = `¥${balance.toFixed(2)}`;
                    balanceColor = '#faad14';
                } else {
                    balanceText = `¥${balance.toFixed(2)}`;
                    balanceColor = '#52c41a';
                }

                const lastOrderTime = getLastOrderTime(customer.name);

                return `
                <tr>
                    <td>${customer.name}</td>
                    <td>¥${customer.unitPrice.toFixed(2)}</td>
                    <td style="color: ${balanceColor}; font-weight: bold;">${balanceText}</td>
                    <td style="font-size: 12px; color: #666;">${lastOrderTime || '暂无'}</td>
                    <td>
                        <button class="btn" style="padding: 4px 8px; margin-right: 4px;" onclick="editCustomer(${customer.id})">编辑</button>
                        <button class="btn" style="padding: 4px 8px; margin-right: 4px; background: #52c41a; color: white;" onclick="quickRecharge(${customer.id})">充值</button>
                        <button class="btn" style="padding: 4px 8px; margin-right: 4px; background: #ff4d4f; color: white;" onclick="addDebt(${customer.id})">增加欠款</button>
                        <button class="btn btn-danger" style="padding: 4px 8px;" onclick="deleteCustomer(${customer.id})">删除</button>
                    </td>
                </tr>
                `;
            }).join('');

            updateCustomerStats();
        }

        function showCustomerModal(customerId = null) {
            editingCustomerId = customerId;
            const modal = document.getElementById('customerModal');
            const title = document.getElementById('customerModalTitle');
            const form = document.getElementById('customerForm');

            if (customerId) {
                const customer = customers.find(c => c.id === customerId);
                title.textContent = '编辑客户';
                document.getElementById('customerName').value = customer.name;
                document.getElementById('customerUnitPrice').value = customer.unitPrice;
                document.getElementById('customerBalance').value = customer.balance || 0;
                // 编辑时隐藏余额字段
                document.getElementById('customerBalance').parentElement.style.display = 'none';
            } else {
                title.textContent = '添加客户';
                form.reset();
                document.getElementById('customerBalance').value = 0;
                // 新建时显示余额字段
                document.getElementById('customerBalance').parentElement.style.display = 'block';
            }

            modal.classList.add('active');
        }

        function closeCustomerModal() {
            document.getElementById('customerModal').classList.remove('active');
            editingCustomerId = null;
        }

        function editCustomer(id) {
            showCustomerModal(id);
        }

        function deleteCustomer(id) {
            if (confirm('确定要删除这个客户吗？')) {
                customers = customers.filter(c => c.id !== id);
                if (isCardView) {
                    renderCustomersCards();
                } else {
                    renderCustomersTable();
                }
                saveToStorage();
            }
        }

        // 图案数据库功能
        function renderPatternsTable() {
            const tbody = document.getElementById('patternsTableBody');
            const searchTerm = document.getElementById('patternSearch').value.toLowerCase();
            const colorFilter = document.getElementById('patternColorFilter').value;

            let filteredPatterns = patterns.filter(pattern => {
                const matchesSearch = pattern.name.toLowerCase().includes(searchTerm);
                const matchesColor = !colorFilter || pattern.colorType === colorFilter;
                return matchesSearch && matchesColor;
            });

            tbody.innerHTML = filteredPatterns.map(pattern => `
                <tr>
                    <td>${pattern.name}</td>
                    <td>${pattern.colorType}</td>
                    <td>${pattern.rowCount}</td>
                    <td>${pattern.actualHeight}mm</td>
                    <td>${pattern.bleedHeight}mm</td>
                    <td>${pattern.customerSquare}</td>
                    <td>
                        <button class="btn" style="padding: 4px 8px; margin-right: 4px;" onclick="editPattern(${pattern.id})">编辑</button>
                        <button class="btn btn-danger" style="padding: 4px 8px;" onclick="deletePattern(${pattern.id})">删除</button>
                    </td>
                </tr>
            `).join('');
        }

        function showPatternModal(patternId = null) {
            editingPatternId = patternId;
            const modal = document.getElementById('patternModal');
            const title = document.getElementById('patternModalTitle');
            const form = document.getElementById('patternForm');

            if (patternId) {
                const pattern = patterns.find(p => p.id === patternId);
                title.textContent = '编辑图案';
                document.getElementById('patternName').value = pattern.name;
                document.getElementById('patternColorType').value = pattern.colorType;
                document.getElementById('patternRowCount').value = pattern.rowCount;
                document.getElementById('patternActualHeight').value = pattern.actualHeight;
                document.getElementById('patternBleedHeight').value = pattern.bleedHeight;
                document.getElementById('patternCustomerSquare').value = pattern.customerSquare;
            } else {
                title.textContent = '添加图案';
                form.reset();
            }

            modal.classList.add('active');
        }

        function closePatternModal() {
            document.getElementById('patternModal').classList.remove('active');
            editingPatternId = null;
        }

        function editPattern(id) {
            showPatternModal(id);
        }

        function deletePattern(id) {
            if (confirm('确定要删除这个图案吗？')) {
                patterns = patterns.filter(p => p.id !== id);
                renderPatternsTable();
                saveToStorage();
            }
        }

        // 耗材管理功能
        function renderMaterialsTable() {
            const tbody = document.getElementById('materialsTableBody');
            const searchTerm = document.getElementById('materialSearch').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;

            let filteredMaterials = materials.filter(material => {
                const matchesSearch = material.name.toLowerCase().includes(searchTerm);
                const matchesCategory = !categoryFilter || material.category === categoryFilter;
                return matchesSearch && matchesCategory;
            });

            tbody.innerHTML = filteredMaterials.map(material => `
                <tr>
                    <td>${material.name}</td>
                    <td>${material.category}</td>
                    <td>${material.unit}</td>
                    <td>¥${material.price.toFixed(2)}</td>
                    <td>${material.stock}</td>
                    <td><span class="status-${material.status === '正常' ? 'normal' : 'low'}">${material.status}</span></td>
                    <td>${material.supplier || '-'}</td>
                    <td>
                        <button class="btn" style="padding: 4px 8px; margin-right: 4px;" onclick="editMaterial(${material.id})">编辑</button>
                        <button class="btn btn-danger" style="padding: 4px 8px;" onclick="deleteMaterial(${material.id})">删除</button>
                    </td>
                </tr>
            `).join('');
        }

        function showMaterialModal(materialId = null) {
            editingMaterialId = materialId;
            const modal = document.getElementById('materialModal');
            const title = document.getElementById('materialModalTitle');
            const form = document.getElementById('materialForm');

            if (materialId) {
                const material = materials.find(m => m.id === materialId);
                title.textContent = '编辑耗材';
                document.getElementById('materialName').value = material.name;
                document.getElementById('materialCategory').value = material.category;
                document.getElementById('materialUnit').value = material.unit;
                document.getElementById('materialPrice').value = material.price;
                document.getElementById('materialStock').value = material.stock;
                document.getElementById('materialSupplier').value = material.supplier;
            } else {
                title.textContent = '添加耗材';
                form.reset();
            }

            modal.classList.add('active');
        }

        function closeMaterialModal() {
            document.getElementById('materialModal').classList.remove('active');
            editingMaterialId = null;
        }

        function editMaterial(id) {
            showMaterialModal(id);
        }

        function deleteMaterial(id) {
            if (confirm('确定要删除这个耗材吗？')) {
                materials = materials.filter(m => m.id !== id);
                renderMaterialsTable();
                saveToStorage();
            }
        }

        // 表单提交处理
        document.getElementById('orderForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // 验证必填字段
            const customerName = document.getElementById('orderCustomerName').value;
            const patternName = document.getElementById('orderPatternName').value;
            const billingType = document.querySelector('input[name="billingType"]:checked')?.value;
            const piecesCount = parseInt(document.getElementById('orderPiecesCount').value) || 0;
            const squareCount = parseFloat(document.getElementById('orderSquareCount').value) || 0;
            const totalPrice = parseFloat(document.getElementById('orderTotalPrice').value) || 0;

            if (!customerName) {
                alert('请选择客户！');
                return;
            }
            if (!patternName) {
                alert('请选择图案！');
                return;
            }
            if (!billingType) {
                alert('请选择计费方式！');
                return;
            }
            if (billingType === 'pieces' && piecesCount <= 0) {
                alert('请输入有效的数量(个)！');
                return;
            }
            if (billingType === 'square' && squareCount <= 0) {
                alert('请输入有效的面积(平方)！');
                return;
            }
            if (totalPrice <= 0) {
                alert('总价格计算错误，请检查输入！');
                return;
            }

            const orderData = {
                customerName: customerName,
                patternName: patternName,
                colorType: document.getElementById('orderColorType').value,
                rowCount: parseInt(document.getElementById('orderRowCount').value) || 0,
                actualHeight: parseFloat(document.getElementById('orderActualHeight').value) || 0,
                bleedHeight: parseFloat(document.getElementById('orderBleedHeight').value) || 0,
                customerSquare: parseInt(document.getElementById('orderCustomerSquare').value) || 0,
                billingType: billingType,
                piecesCount: piecesCount,
                squareCount: squareCount,
                unitPrice: parseFloat(document.getElementById('orderUnitPrice').value) || 0,
                totalPrice: totalPrice,
                createTime: document.getElementById('orderDate').value + ' ' + new Date().toTimeString().slice(0, 8)
            };

            if (editingOrderId) {
                // 编辑订单时，先恢复原订单的余额，再扣减新订单的余额
                const oldOrder = orders.find(o => o.id === editingOrderId);
                const customer = customers.find(c => c.name === customerName);

                if (customer && oldOrder) {
                    // 恢复原订单金额
                    customer.balance = (customer.balance || 0) + oldOrder.totalPrice;
                    // 扣减新订单金额
                    customer.balance = customer.balance - totalPrice;
                }

                const index = orders.findIndex(o => o.id === editingOrderId);
                orders[index] = { ...orders[index], ...orderData };
            } else {
                // 新建订单时，扣减客户余额
                const customer = customers.find(c => c.name === customerName);
                if (customer) {
                    customer.balance = (customer.balance || 0) - totalPrice;

                    // 添加消费记录
                    const record = {
                        id: Date.now() + Math.random(),
                        customerId: customer.id,
                        customerName: customer.name,
                        amount: -totalPrice, // 负数表示消费
                        type: 'order',
                        description: `订单消费 - ${patternName}`,
                        createTime: orderData.createTime
                    };
                    if (!window.rechargeRecords) {
                        window.rechargeRecords = [];
                    }
                    rechargeRecords.push(record);
                }

                orderData.id = Date.now();
                orders.push(orderData);
            }

            closeOrderModal();
            renderOrdersTable();
            // 更新客户表格显示余额变化
            if (isCardView) {
                renderCustomersCards();
            } else {
                renderCustomersTable();
            }

            // 如果当前在客户详情页面，更新详情信息和订单列表
            if (currentCustomerId) {
                const customer = customers.find(c => c.name === customerName);
                if (customer && customer.id === currentCustomerId) {
                    renderCustomerDetailInfo();
                    renderCustomerOrders();
                }
            }

            saveToStorage();
            updateDashboard();

            // 显示余额变化提醒
            const customer = customers.find(c => c.name === customerName);
            if (customer) {
                const balance = customer.balance || 0;
                if (balance < 0) {
                    alert(`订单保存成功！\n客户 ${customer.name} 当前欠款：¥${Math.abs(balance).toFixed(2)}`);
                } else if (balance < 100) {
                    alert(`订单保存成功！\n客户 ${customer.name} 余额不足：¥${balance.toFixed(2)}，建议及时充值`);
                } else {
                    alert(`订单保存成功！\n客户 ${customer.name} 当前余额：¥${balance.toFixed(2)}`);
                }
            }
        });

        document.getElementById('customerForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const customerData = {
                name: document.getElementById('customerName').value,
                unitPrice: parseFloat(document.getElementById('customerUnitPrice').value)
            };

            if (editingCustomerId) {
                const index = customers.findIndex(c => c.id === editingCustomerId);
                customers[index] = { ...customers[index], ...customerData };
            } else {
                customerData.id = Date.now();
                customerData.balance = parseFloat(document.getElementById('customerBalance').value) || 0;
                customers.push(customerData);
            }

            closeCustomerModal();
            if (isCardView) {
                renderCustomersCards();
            } else {
                renderCustomersTable();
            }
            saveToStorage();
        });

        document.getElementById('patternForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const patternData = {
                name: document.getElementById('patternName').value,
                colorType: document.getElementById('patternColorType').value,
                rowCount: parseInt(document.getElementById('patternRowCount').value),
                actualHeight: parseFloat(document.getElementById('patternActualHeight').value),
                bleedHeight: parseFloat(document.getElementById('patternBleedHeight').value),
                customerSquare: parseFloat(document.getElementById('patternCustomerSquare').value)
            };

            if (editingPatternId) {
                const index = patterns.findIndex(p => p.id === editingPatternId);
                patterns[index] = { ...patterns[index], ...patternData };
            } else {
                patternData.id = Date.now();
                patterns.push(patternData);
            }

            closePatternModal();
            renderPatternsTable();
            saveToStorage();
        });

        document.getElementById('materialForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const materialData = {
                name: document.getElementById('materialName').value,
                category: document.getElementById('materialCategory').value,
                unit: document.getElementById('materialUnit').value,
                price: parseFloat(document.getElementById('materialPrice').value),
                stock: parseInt(document.getElementById('materialStock').value),
                supplier: document.getElementById('materialSupplier').value,
                status: parseInt(document.getElementById('materialStock').value) < 10 ? '库存不足' : '正常'
            };

            if (editingMaterialId) {
                const index = materials.findIndex(m => m.id === editingMaterialId);
                materials[index] = { ...materials[index], ...materialData };
            } else {
                materialData.id = Date.now();
                materials.push(materialData);
            }

            closeMaterialModal();
            renderMaterialsTable();
            saveToStorage();
        });

        // 数据存储功能
        function saveToStorage() {
            const data = {
                orders: orders,
                customers: customers,
                patterns: patterns,
                materials: materials,
                rechargeRecords: rechargeRecords,
                lastBackup: new Date().toISOString()
            };
            localStorage.setItem('printSystemData', JSON.stringify(data));
        }

        function loadFromStorage() {
            const data = localStorage.getItem('printSystemData');
            console.log('尝试加载数据，存储数据：', data ? '存在' : '不存在');

            if (data) {
                try {
                    const parsed = JSON.parse(data);
                    orders = parsed.orders || orders;
                    customers = parsed.customers || customers;
                    patterns = parsed.patterns || patterns;
                    materials = parsed.materials || materials;
                    rechargeRecords = parsed.rechargeRecords || rechargeRecords;

                    console.log('数据加载完成 - 订单:', orders.length, '客户:', customers.length, '图案:', patterns.length, '耗材:', materials.length, '充值记录:', rechargeRecords.length);

                    if (parsed.lastBackup) {
                        const lastBackupElement = document.getElementById('lastBackupTime');
                        if (lastBackupElement) {
                            lastBackupElement.textContent = new Date(parsed.lastBackup).toLocaleString();
                        }
                    }
                } catch (error) {
                    console.error('解析存储数据失败:', error);
                }
            } else {
                console.log('没有找到存储数据，使用默认数据');
                console.log('默认数据 - 订单:', orders.length, '客户:', customers.length, '图案:', patterns.length, '耗材:', materials.length);
            }
        }

        // 数据管理功能
        function exportData() {
            const data = {
                orders: orders,
                customers: customers,
                patterns: patterns,
                materials: materials,
                rechargeRecords: rechargeRecords,
                exportTime: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `白墨打印统计系统_${new Date().toISOString().slice(0, 10)}.json`;
            a.click();
            URL.revokeObjectURL(url);

            // 更新最后备份时间
            document.getElementById('lastBackupTime').textContent = new Date().toLocaleString();
            saveToStorage();
        }

        function importData(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = JSON.parse(e.target.result);
                        if (confirm('导入数据将覆盖现有数据，确定要继续吗？')) {
                            orders = data.orders || [];
                            customers = data.customers || [];
                            patterns = data.patterns || [];
                            materials = data.materials || [];
                            rechargeRecords = data.rechargeRecords || [];

                            saveToStorage();
                            updateDashboard();
                            alert('数据导入成功！');
                        }
                    } catch (error) {
                        alert('文件格式错误，请选择正确的JSON文件！');
                    }
                };
                reader.readAsText(file);
            }
        }

        function clearAllData() {
            if (confirm('确定要清空所有数据吗？此操作不可恢复！')) {
                if (confirm('请再次确认：这将删除所有订单、客户、图案和耗材数据！')) {
                    orders = [];
                    customers = [];
                    patterns = [];
                    materials = [];
                    rechargeRecords = [];

                    localStorage.removeItem('printSystemData');
                    updateDashboard();
                    alert('所有数据已清空！');
                }
            }
        }

        // Excel解析工具函数
        function parseExcel(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });

                        // 获取第一个工作表
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];

                        // 转换为JSON数组
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                        // 过滤空行
                        const filteredData = jsonData.filter(row =>
                            row && row.length > 0 && row.some(cell => cell !== null && cell !== undefined && cell !== '')
                        );

                        resolve(filteredData);
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsArrayBuffer(file);
            });
        }

        // CSV解析工具函数（保留作为备用）
        function parseCSV(csvText) {
            const lines = csvText.split('\n').filter(line => line.trim());
            const result = [];

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;

                const values = [];
                let current = '';
                let inQuotes = false;

                for (let j = 0; j < line.length; j++) {
                    const char = line[j];

                    if (char === '"') {
                        inQuotes = !inQuotes;
                    } else if (char === ',' && !inQuotes) {
                        values.push(current.trim());
                        current = '';
                    } else {
                        current += char;
                    }
                }
                values.push(current.trim());
                result.push(values);
            }

            return result;
        }

        // 显示导入结果
        function showImportResult(type, success, failed, errors = []) {
            const resultDiv = document.getElementById('importResult');
            const isSuccess = failed === 0;

            resultDiv.style.display = 'block';
            resultDiv.style.background = isSuccess ? '#f6ffed' : '#fff2f0';
            resultDiv.style.border = `1px solid ${isSuccess ? '#b7eb8f' : '#ffccc7'}`;
            resultDiv.style.color = isSuccess ? '#52c41a' : '#ff4d4f';

            let html = `
                <h4 style="margin: 0 0 8px 0;">${type}导入结果</h4>
                <p style="margin: 0 0 8px 0;">
                    ✅ 成功导入：${success} 条
                    ${failed > 0 ? `<br>❌ 失败：${failed} 条` : ''}
                </p>
            `;

            if (errors.length > 0) {
                html += `
                    <details style="margin-top: 8px;">
                        <summary style="cursor: pointer; color: #ff4d4f;">查看错误详情</summary>
                        <ul style="margin: 8px 0 0 20px; font-size: 12px;">
                            ${errors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    </details>
                `;
            }

            resultDiv.innerHTML = html;

            // 3秒后自动隐藏成功消息
            if (isSuccess) {
                setTimeout(() => {
                    resultDiv.style.display = 'none';
                }, 3000);
            }
        }

        // 模板下载功能
        function downloadCustomerTemplate() {
            const template = [
                ['客户姓名', '客户单价'],
                ['张三', 100],
                ['李四', 120],
                ['王五', 90]
            ];
            downloadExcel(template, '客户导入模板.xlsx');
        }

        function downloadPatternTemplate() {
            const template = [
                ['图案名称', '色号', '行数量', '实际高度'],
                ['花朵图案', '单色', 10, 50],
                ['几何图案', '双色', 8, 60],
                ['抽象图案', '三色', 12, 45]
            ];
            downloadExcel(template, '图案导入模板.xlsx');
        }

        function downloadOrderTemplate() {
            const template = [
                ['客户姓名', '图案名称', '计费方式', '数量', '创建时间'],
                ['张三', '花朵图案', 'pieces', 100, '2025-01-28 10:30:00'],
                ['李四', '几何图案', 'square', 5.5, '2025-01-28 14:30:00'],
                ['王五', '抽象图案', 'pieces', 200, '2025-01-28 16:00:00']
            ];
            downloadExcel(template, '订单导入模板.xlsx');
        }

        function downloadExcel(data, filename) {
            // 创建工作簿
            const wb = XLSX.utils.book_new();

            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet(data);

            // 设置列宽
            const colWidths = [];
            data[0].forEach((_, index) => {
                let maxWidth = 0;
                data.forEach(row => {
                    if (row[index]) {
                        const cellLength = String(row[index]).length;
                        maxWidth = Math.max(maxWidth, cellLength);
                    }
                });
                colWidths.push({ wch: Math.max(maxWidth + 2, 10) });
            });
            ws['!cols'] = colWidths;

            // 设置标题行样式
            const headerStyle = {
                font: { bold: true },
                fill: { fgColor: { rgb: "E6F3FF" } },
                alignment: { horizontal: "center" }
            };

            // 应用标题行样式
            data[0].forEach((_, index) => {
                const cellRef = XLSX.utils.encode_cell({ r: 0, c: index });
                if (!ws[cellRef]) ws[cellRef] = {};
                ws[cellRef].s = headerStyle;
            });

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(wb, ws, "数据");

            // 下载文件
            XLSX.writeFile(wb, filename);
        }

        function downloadCSV(data, filename) {
            const csvContent = data.map(row =>
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 批量导入客户
        async function importCustomerBatch(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                const excelData = await parseExcel(file);
                if (excelData.length < 2) {
                    alert('文件格式错误：至少需要包含标题行和一行数据');
                    return;
                }

                const headers = excelData[0];
                const expectedHeaders = ['客户姓名', '客户单价'];

                // 验证标题行
                if (!expectedHeaders.every(header => headers.includes(header))) {
                    alert('文件格式错误：标题行必须包含：' + expectedHeaders.join(', '));
                    return;
                }

                let successCount = 0;
                let failedCount = 0;
                const errors = [];

                for (let i = 1; i < excelData.length; i++) {
                    const row = excelData[i];
                    if (row.length < 2) {
                        errors.push(`第${i+1}行：数据不完整`);
                        failedCount++;
                        continue;
                    }

                    const [name, unitPrice] = row;

                    // 验证必填字段
                    if (!name || unitPrice === null || unitPrice === undefined || unitPrice === '') {
                        errors.push(`第${i+1}行：客户姓名和单价不能为空`);
                        failedCount++;
                        continue;
                    }

                    // 验证单价格式
                    const price = parseFloat(unitPrice);
                    if (isNaN(price) || price <= 0) {
                        errors.push(`第${i+1}行：客户单价格式错误`);
                        failedCount++;
                        continue;
                    }

                    // 检查是否已存在
                    if (customers.find(c => c.name === String(name).trim())) {
                        errors.push(`第${i+1}行：客户"${name}"已存在`);
                        failedCount++;
                        continue;
                    }

                    // 添加客户
                    const customer = {
                        id: Date.now() + Math.random(),
                        name: String(name).trim(),
                        unitPrice: price
                    };

                    customers.push(customer);
                    successCount++;
                }

                saveToStorage();
                renderCustomersTable();
                showImportResult('客户', successCount, failedCount, errors);

            } catch (error) {
                alert('文件解析失败：' + error.message);
            }

            event.target.value = ''; // 清空文件选择
        }

        // 批量导入图案
        async function importPatternBatch(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                const excelData = await parseExcel(file);
                if (excelData.length < 2) {
                    alert('文件格式错误：至少需要包含标题行和一行数据');
                    return;
                }

                const headers = excelData[0];
                const expectedHeaders = ['图案名称', '色号', '行数量', '实际高度'];

                if (!expectedHeaders.every(header => headers.includes(header))) {
                    alert('文件格式错误：标题行必须包含：' + expectedHeaders.join(', '));
                    return;
                }

                let successCount = 0;
                let failedCount = 0;
                const errors = [];

                for (let i = 1; i < excelData.length; i++) {
                    const row = excelData[i];
                    if (row.length < 4) {
                        errors.push(`第${i+1}行：数据不完整`);
                        failedCount++;
                        continue;
                    }

                    const [name, colorType, rowCount, actualHeight] = row;

                    // 验证必填字段
                    if (!name || !colorType || rowCount === null || rowCount === undefined ||
                        actualHeight === null || actualHeight === undefined) {
                        errors.push(`第${i+1}行：所有字段都不能为空`);
                        failedCount++;
                        continue;
                    }

                    // 验证色号
                    if (!['单色', '双色', '三色'].includes(String(colorType).trim())) {
                        errors.push(`第${i+1}行：色号必须是"单色"、"双色"或"三色"`);
                        failedCount++;
                        continue;
                    }

                    // 验证数值格式
                    const rows = parseInt(rowCount);
                    const height = parseFloat(actualHeight);

                    if (isNaN(rows) || rows <= 0) {
                        errors.push(`第${i+1}行：行数量格式错误`);
                        failedCount++;
                        continue;
                    }

                    if (isNaN(height) || height <= 0) {
                        errors.push(`第${i+1}行：实际高度格式错误`);
                        failedCount++;
                        continue;
                    }

                    // 检查是否已存在
                    if (patterns.find(p => p.name === String(name).trim())) {
                        errors.push(`第${i+1}行：图案"${name}"已存在`);
                        failedCount++;
                        continue;
                    }

                    // 计算相关数据
                    const bleedHeight = height + 20;
                    const customerSquare = Math.floor((1600 / bleedHeight) * rows);

                    // 添加图案
                    const pattern = {
                        id: Date.now() + Math.random(),
                        name: String(name).trim(),
                        colorType: String(colorType).trim(),
                        rowCount: rows,
                        actualHeight: height,
                        bleedHeight: bleedHeight,
                        customerSquare: customerSquare
                    };

                    patterns.push(pattern);
                    successCount++;
                }

                saveToStorage();
                renderPatternsTable();
                showImportResult('图案', successCount, failedCount, errors);

            } catch (error) {
                alert('文件解析失败：' + error.message);
            }

            event.target.value = '';
        }

        // 批量导入订单
        async function importOrderBatch(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                const excelData = await parseExcel(file);
                if (excelData.length < 2) {
                    alert('文件格式错误：至少需要包含标题行和一行数据');
                    return;
                }

                const headers = excelData[0];
                const expectedHeaders = ['客户姓名', '图案名称', '计费方式', '数量', '创建时间'];

                if (!expectedHeaders.every(header => headers.includes(header))) {
                    alert('文件格式错误：标题行必须包含：' + expectedHeaders.join(', '));
                    return;
                }

                let successCount = 0;
                let failedCount = 0;
                const errors = [];

                for (let i = 1; i < excelData.length; i++) {
                    const row = excelData[i];
                    if (row.length < 4) { // 创建时间可选
                        errors.push(`第${i+1}行：数据不完整`);
                        failedCount++;
                        continue;
                    }

                    const [customerName, patternName, billingType, quantity, createTime] = row;

                    // 验证必填字段
                    if (!customerName || !patternName || !billingType ||
                        quantity === null || quantity === undefined || quantity === '') {
                        errors.push(`第${i+1}行：客户姓名、图案名称、计费方式和数量不能为空`);
                        failedCount++;
                        continue;
                    }

                    // 验证计费方式
                    if (!['pieces', 'square'].includes(String(billingType).trim())) {
                        errors.push(`第${i+1}行：计费方式必须是"pieces"或"square"`);
                        failedCount++;
                        continue;
                    }

                    // 验证数量格式
                    const qty = parseFloat(quantity);
                    if (isNaN(qty) || qty <= 0) {
                        errors.push(`第${i+1}行：数量格式错误`);
                        failedCount++;
                        continue;
                    }

                    // 检查客户是否存在
                    const customer = customers.find(c => c.name === String(customerName).trim());
                    if (!customer) {
                        errors.push(`第${i+1}行：客户"${customerName}"不存在`);
                        failedCount++;
                        continue;
                    }

                    // 检查图案是否存在
                    const pattern = patterns.find(p => p.name === String(patternName).trim());
                    if (!pattern) {
                        errors.push(`第${i+1}行：图案"${patternName}"不存在`);
                        failedCount++;
                        continue;
                    }

                    // 计算价格
                    let unitPrice = 0;
                    let totalPrice = 0;
                    let piecesCount = 0;
                    let squareCount = 0;

                    const billing = String(billingType).trim();
                    if (billing === 'pieces') {
                        piecesCount = Math.floor(qty);
                        unitPrice = customer.unitPrice / pattern.customerSquare;
                        totalPrice = unitPrice * piecesCount;
                    } else {
                        squareCount = qty;
                        unitPrice = 16;
                        totalPrice = unitPrice * squareCount;
                    }

                    // 处理创建时间
                    let orderCreateTime = createTime ? String(createTime).trim() : new Date().toISOString().replace('T', ' ').slice(0, 19);

                    // 添加订单
                    const order = {
                        id: Date.now() + Math.random(),
                        customerName: String(customerName).trim(),
                        patternName: String(patternName).trim(),
                        colorType: pattern.colorType,
                        rowCount: pattern.rowCount,
                        actualHeight: pattern.actualHeight,
                        bleedHeight: pattern.bleedHeight,
                        customerSquare: pattern.customerSquare,
                        billingType: billing,
                        piecesCount: piecesCount,
                        squareCount: squareCount,
                        unitPrice: parseFloat(unitPrice.toFixed(2)),
                        totalPrice: parseFloat(totalPrice.toFixed(2)),
                        createTime: orderCreateTime
                    };

                    orders.push(order);
                    successCount++;
                }

                saveToStorage();
                renderOrdersTable();
                updateDashboard();
                showImportResult('订单', successCount, failedCount, errors);

            } catch (error) {
                alert('文件解析失败：' + error.message);
            }

            event.target.value = '';
        }

        // 财务管理功能

        // 显示充值模态框
        function showRechargeModal(customerId = null) {
            const modal = document.getElementById('rechargeModal');
            const customerSelect = document.getElementById('rechargeCustomer');
            const form = document.getElementById('rechargeForm');

            // 加载客户选项
            customerSelect.innerHTML = '<option value="">请选择客户</option>';
            customers.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.id;
                option.textContent = customer.name;
                customerSelect.appendChild(option);
            });

            // 如果指定了客户ID，自动选择
            if (customerId) {
                customerSelect.value = customerId;
                updateCurrentBalance();
            }

            form.reset();
            if (customerId) {
                customerSelect.value = customerId;
                updateCurrentBalance();
            }

            modal.classList.add('active');
        }

        // 快速充值
        function quickRecharge(customerId) {
            showRechargeModal(customerId);
        }

        // 更新当前余额显示
        function updateCurrentBalance() {
            const customerId = parseInt(document.getElementById('rechargeCustomer').value);
            const customer = customers.find(c => c.id === customerId);
            const balanceInput = document.getElementById('currentBalance');

            if (customer) {
                balanceInput.value = `¥${(customer.balance || 0).toFixed(2)}`;
            } else {
                balanceInput.value = '';
            }
        }

        // 关闭充值模态框
        function closeRechargeModal() {
            document.getElementById('rechargeModal').classList.remove('active');
        }

        // 充值表单提交
        document.getElementById('rechargeForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const customerId = parseInt(document.getElementById('rechargeCustomer').value);
            const amount = parseFloat(document.getElementById('rechargeAmount').value);
            const description = document.getElementById('rechargeDescription').value || '账户充值';

            if (!customerId || !amount || amount <= 0) {
                alert('请选择客户并输入有效的充值金额');
                return;
            }

            // 更新客户余额
            const customer = customers.find(c => c.id === customerId);
            if (customer) {
                customer.balance = (customer.balance || 0) + amount;

                // 添加充值记录
                const record = {
                    id: Date.now(),
                    customerId: customerId,
                    customerName: customer.name,
                    amount: amount,
                    type: 'recharge',
                    description: description,
                    createTime: new Date().toISOString().replace('T', ' ').slice(0, 19)
                };
                rechargeRecords.push(record);

                saveToStorage();
                if (isCardView) {
                    renderCustomersCards();
                } else {
                    renderCustomersTable();
                }
                updateFinanceDashboard();

                // 如果当前在客户详情页面，更新详情信息
                if (currentCustomerId === customerId) {
                    renderCustomerDetailInfo();
                }

                closeRechargeModal();

                alert(`充值成功！${customer.name} 账户余额：¥${customer.balance.toFixed(2)}`);
            }
        });

        // 客户选择变化时更新余额
        document.getElementById('rechargeCustomer').addEventListener('change', updateCurrentBalance);

        // 增加欠款功能
        function addDebt(customerId) {
            showDebtModal(customerId);
        }

        // 显示增加欠款模态框
        function showDebtModal(customerId = null) {
            const modal = document.getElementById('debtModal');
            const customerSelect = document.getElementById('debtCustomer');
            const form = document.getElementById('debtForm');

            // 加载客户选项
            customerSelect.innerHTML = '<option value="">请选择客户</option>';
            customers.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.id;
                option.textContent = customer.name;
                customerSelect.appendChild(option);
            });

            // 如果指定了客户ID，自动选择
            if (customerId) {
                customerSelect.value = customerId;
                updateCurrentDebt();
            }

            form.reset();
            if (customerId) {
                customerSelect.value = customerId;
                updateCurrentDebt();
            }

            modal.classList.add('active');
        }

        // 更新当前余额显示（欠款模态框）
        function updateCurrentDebt() {
            const customerId = parseInt(document.getElementById('debtCustomer').value);
            const customer = customers.find(c => c.id === customerId);
            const balanceInput = document.getElementById('currentDebt');

            if (customer) {
                const balance = customer.balance || 0;
                if (balance < 0) {
                    balanceInput.value = `欠款 ¥${Math.abs(balance).toFixed(2)}`;
                } else {
                    balanceInput.value = `¥${balance.toFixed(2)}`;
                }
            } else {
                balanceInput.value = '';
            }
        }

        // 关闭增加欠款模态框
        function closeDebtModal() {
            document.getElementById('debtModal').classList.remove('active');
        }

        // 增加欠款表单提交
        document.getElementById('debtForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const customerId = parseInt(document.getElementById('debtCustomer').value);
            const amount = parseFloat(document.getElementById('debtAmount').value);
            const description = document.getElementById('debtDescription').value;

            if (!customerId || !amount || amount <= 0 || !description.trim()) {
                alert('请选择客户、输入有效的欠款金额和欠款原因');
                return;
            }

            // 更新客户余额（减少余额，增加欠款）
            const customer = customers.find(c => c.id === customerId);
            if (customer) {
                customer.balance = (customer.balance || 0) - amount;

                // 添加欠款记录
                const record = {
                    id: Date.now(),
                    customerId: customerId,
                    customerName: customer.name,
                    amount: -amount, // 负数表示欠款
                    type: 'debt',
                    description: description,
                    createTime: new Date().toISOString().replace('T', ' ').slice(0, 19)
                };
                rechargeRecords.push(record);

                saveToStorage();
                if (isCardView) {
                    renderCustomersCards();
                } else {
                    renderCustomersTable();
                }
                updateFinanceDashboard();

                // 如果当前在客户详情页面，更新详情信息
                if (currentCustomerId === customerId) {
                    renderCustomerDetailInfo();
                }

                closeDebtModal();

                const newBalance = customer.balance;
                if (newBalance < 0) {
                    alert(`欠款增加成功！${customer.name} 当前欠款：¥${Math.abs(newBalance).toFixed(2)}`);
                } else {
                    alert(`欠款增加成功！${customer.name} 当前余额：¥${newBalance.toFixed(2)}`);
                }
            }
        });

        // 客户选择变化时更新余额（欠款模态框）
        document.getElementById('debtCustomer').addEventListener('change', updateCurrentDebt);

        // 显示余额报表
        function showBalanceReport() {
            const modal = document.getElementById('financeReportModal');
            const title = document.getElementById('financeReportModalTitle');
            const content = document.getElementById('financeReportContent');

            title.textContent = '客户余额报表';

            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>客户姓名</th>
                            <th>账户余额</th>
                            <th>余额状态</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            customers.forEach(customer => {
                const balance = customer.balance || 0;
                let status, statusColor, balanceText;

                if (balance < 0) {
                    status = '欠款';
                    statusColor = '#cf1322';
                    balanceText = `欠款 ¥${Math.abs(balance).toFixed(2)}`;
                } else if (balance < 100) {
                    status = '不足';
                    statusColor = '#ff4d4f';
                    balanceText = `¥${balance.toFixed(2)}`;
                } else if (balance < 300) {
                    status = '偏低';
                    statusColor = '#faad14';
                    balanceText = `¥${balance.toFixed(2)}`;
                } else {
                    status = '充足';
                    statusColor = '#52c41a';
                    balanceText = `¥${balance.toFixed(2)}`;
                }

                html += `
                    <tr>
                        <td>${customer.name}</td>
                        <td style="color: ${balance < 0 ? '#cf1322' : '#000'}; font-weight: ${balance < 0 ? 'bold' : 'normal'};">${balanceText}</td>
                        <td style="color: ${statusColor}; font-weight: bold;">${status}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            content.innerHTML = html;
            modal.classList.add('active');
        }

        // 显示充值记录
        function showRechargeHistory() {
            const modal = document.getElementById('financeReportModal');
            const title = document.getElementById('financeReportModalTitle');
            const content = document.getElementById('financeReportContent');

            title.textContent = '充值记录';

            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>客户</th>
                            <th>类型</th>
                            <th>金额</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            const sortedRecords = [...rechargeRecords].sort((a, b) =>
                new Date(b.createTime) - new Date(a.createTime)
            );

            sortedRecords.forEach(record => {
                const typeText = record.type === 'recharge' ? '充值' : '消费';
                const typeColor = record.type === 'recharge' ? '#52c41a' : '#ff4d4f';
                const amountPrefix = record.type === 'recharge' ? '+' : '-';

                html += `
                    <tr>
                        <td>${record.createTime}</td>
                        <td>${record.customerName}</td>
                        <td style="color: ${typeColor};">${typeText}</td>
                        <td style="color: ${typeColor};">${amountPrefix}¥${record.amount.toFixed(2)}</td>
                        <td>${record.description}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            content.innerHTML = html;
            modal.classList.add('active');
        }

        // 关闭财务报表模态框
        function closeFinanceReportModal() {
            document.getElementById('financeReportModal').classList.remove('active');
        }

        // 显示收入明细报表
        function showIncomeReport() {
            const modal = document.getElementById('financeReportModal');
            const title = document.getElementById('financeReportModalTitle');
            const content = document.getElementById('financeReportContent');

            title.textContent = '收入明细报表';

            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>客户</th>
                            <th>图案</th>
                            <th>计费方式</th>
                            <th>数量</th>
                            <th>单价</th>
                            <th>总价</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            const sortedOrders = [...orders].sort((a, b) =>
                new Date(b.createTime) - new Date(a.createTime)
            );

            let totalIncome = 0;
            sortedOrders.forEach(order => {
                totalIncome += order.totalPrice;
                const billingText = order.billingType === 'pieces' ? '按个数' : '按平方';
                const quantity = order.billingType === 'pieces' ? order.piecesCount : order.squareCount;
                const unit = order.billingType === 'pieces' ? '个' : '㎡';

                html += `
                    <tr>
                        <td>${order.createTime}</td>
                        <td>${order.customerName}</td>
                        <td>${order.patternName}</td>
                        <td>${billingText}</td>
                        <td>${quantity}${unit}</td>
                        <td>¥${order.unitPrice.toFixed(2)}</td>
                        <td>¥${order.totalPrice.toFixed(2)}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                    <tfoot>
                        <tr style="background: #f0f0f0; font-weight: bold;">
                            <td colspan="6">总收入</td>
                            <td>¥${totalIncome.toFixed(2)}</td>
                        </tr>
                    </tfoot>
                </table>
            `;

            content.innerHTML = html;
            modal.classList.add('active');
        }

        // 显示客户排行
        function showCustomerRanking() {
            const modal = document.getElementById('financeReportModal');
            const title = document.getElementById('financeReportModalTitle');
            const content = document.getElementById('financeReportContent');

            title.textContent = '客户消费排行';

            // 统计每个客户的消费
            const customerStats = {};
            orders.forEach(order => {
                if (!customerStats[order.customerName]) {
                    customerStats[order.customerName] = {
                        name: order.customerName,
                        totalAmount: 0,
                        orderCount: 0
                    };
                }
                customerStats[order.customerName].totalAmount += order.totalPrice;
                customerStats[order.customerName].orderCount++;
            });

            // 排序
            const sortedCustomers = Object.values(customerStats).sort((a, b) => b.totalAmount - a.totalAmount);

            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>客户姓名</th>
                            <th>订单数量</th>
                            <th>消费总额</th>
                            <th>平均订单金额</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            sortedCustomers.forEach((customer, index) => {
                const avgAmount = customer.totalAmount / customer.orderCount;
                html += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${customer.name}</td>
                        <td>${customer.orderCount}</td>
                        <td>¥${customer.totalAmount.toFixed(2)}</td>
                        <td>¥${avgAmount.toFixed(2)}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            content.innerHTML = html;
            modal.classList.add('active');
        }

        // 显示图案销量排行
        function showPatternRanking() {
            const modal = document.getElementById('financeReportModal');
            const title = document.getElementById('financeReportModalTitle');
            const content = document.getElementById('financeReportContent');

            title.textContent = '图案销量排行';

            // 统计每个图案的销量
            const patternStats = {};
            orders.forEach(order => {
                if (!patternStats[order.patternName]) {
                    patternStats[order.patternName] = {
                        name: order.patternName,
                        colorType: order.colorType,
                        totalAmount: 0,
                        orderCount: 0,
                        totalPieces: 0,
                        totalSquare: 0
                    };
                }
                patternStats[order.patternName].totalAmount += order.totalPrice;
                patternStats[order.patternName].orderCount++;
                patternStats[order.patternName].totalPieces += order.piecesCount || 0;
                patternStats[order.patternName].totalSquare += order.squareCount || 0;
            });

            // 排序
            const sortedPatterns = Object.values(patternStats).sort((a, b) => b.totalAmount - a.totalAmount);

            let html = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>图案名称</th>
                            <th>色号</th>
                            <th>订单数量</th>
                            <th>销售总额</th>
                            <th>总个数</th>
                            <th>总平方</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            sortedPatterns.forEach((pattern, index) => {
                html += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${pattern.name}</td>
                        <td>${pattern.colorType}</td>
                        <td>${pattern.orderCount}</td>
                        <td>¥${pattern.totalAmount.toFixed(2)}</td>
                        <td>${pattern.totalPieces}</td>
                        <td>${pattern.totalSquare.toFixed(2)}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            content.innerHTML = html;
            modal.classList.add('active');
        }

        // 导出财务报表
        function exportFinanceReport() {
            const reportData = [
                ['财务报表导出', '', '', '', '', '', ''],
                ['导出时间：' + new Date().toLocaleString(), '', '', '', '', '', ''],
                ['', '', '', '', '', '', ''],
                ['收入明细', '', '', '', '', '', ''],
                ['时间', '客户', '图案', '计费方式', '数量', '单价', '总价']
            ];

            let totalIncome = 0;
            orders.forEach(order => {
                totalIncome += order.totalPrice;
                const billingText = order.billingType === 'pieces' ? '按个数' : '按平方';
                const quantity = order.billingType === 'pieces' ? order.piecesCount : order.squareCount;
                const unit = order.billingType === 'pieces' ? '个' : '㎡';

                reportData.push([
                    order.createTime,
                    order.customerName,
                    order.patternName,
                    billingText,
                    quantity + unit,
                    order.unitPrice.toFixed(2),
                    order.totalPrice.toFixed(2)
                ]);
            });

            reportData.push(['', '', '', '', '', '总收入：', totalIncome.toFixed(2)]);

            downloadExcel(reportData, `财务报表_${new Date().toISOString().slice(0, 10)}.xlsx`);
        }

        // 搜索功能 - 将在初始化时设置

        // 更新数据统计
        function updateDataStats() {
            const stats = `
                订单数量：${orders.length} 个<br>
                客户数量：${customers.length} 个<br>
                图案数量：${patterns.length} 个<br>
                耗材数量：${materials.length} 个
            `;
            document.getElementById('dataStats').innerHTML = stats;
        }

        // 初始化系统
        function initSystem() {
            console.log('开始初始化系统...');

            loadFromStorage();

            // 如果没有数据，添加一个测试订单
            if (orders.length === 0) {
                console.log('没有订单数据，添加测试订单');
                const testOrder = {
                    id: Date.now(),
                    customerName: '测试客户',
                    patternName: '测试图案',
                    colorType: '单色',
                    billingType: 'pieces',
                    piecesCount: 10,
                    squareCount: 0,
                    rowCount: 2,
                    actualHeight: 50,
                    bleedHeight: 70,
                    customerSquare: 45,
                    unitPrice: 2.22,
                    totalPrice: 22.20,
                    createTime: new Date().toLocaleString()
                };
                orders.push(testOrder);
                saveToStorage();
            }

            updateDashboard();
            updateFinanceData();
            updateDataStats();
            updateFinanceDashboard();

            // 确保所有表格都正确渲染
            console.log('开始渲染表格...');
            renderOrdersTable();
            renderCustomersCards(); // 默认使用卡片视图
            renderPatternsTable();
            renderMaterialsTable();

            // 设置搜索功能事件监听器
            console.log('设置事件监听器...');
            const orderSearch = document.getElementById('orderSearch');
            const colorFilter = document.getElementById('colorFilter');
            const customerSearch = document.getElementById('customerSearch');
            const patternSearch = document.getElementById('patternSearch');
            const patternColorFilter = document.getElementById('patternColorFilter');
            const materialSearch = document.getElementById('materialSearch');
            const categoryFilter = document.getElementById('categoryFilter');

            if (orderSearch) orderSearch.addEventListener('input', renderOrdersTable);
            if (colorFilter) colorFilter.addEventListener('change', renderOrdersTable);
            if (customerSearch) customerSearch.addEventListener('input', () => {
                if (isCardView) {
                    renderCustomersCards();
                } else {
                    renderCustomersTable();
                }
            });
            if (patternSearch) patternSearch.addEventListener('input', renderPatternsTable);
            if (patternColorFilter) patternColorFilter.addEventListener('change', renderPatternsTable);
            if (materialSearch) materialSearch.addEventListener('input', renderMaterialsTable);
            if (categoryFilter) categoryFilter.addEventListener('change', renderMaterialsTable);

            // 设置默认页面
            showPage('dashboard');

            console.log('系统初始化完成');
        }

        // 更新财务仪表板
        function updateFinanceDashboard() {
            // 更新客户余额卡片
            updateCustomerBalances();

            // 更新余额预警
            updateBalanceWarning();

            // 更新成本分析
            updateCostAnalysis();
        }

        // 更新客户余额卡片
        function updateCustomerBalances() {
            const container = document.getElementById('customerBalances');
            const summaryContainer = document.getElementById('balanceSummary');
            const filterSelect = document.getElementById('balanceFilter');
            if (!container) return;

            const filterValue = filterSelect ? filterSelect.value : 'all';

            // 统计各类客户数量
            let debtCount = 0, lowCount = 0, normalCount = 0, goodCount = 0;
            let totalDebt = 0, totalBalance = 0;

            let html = '';
            customers.forEach(customer => {
                const balance = customer.balance || 0;
                let balanceText, balanceColor, statusText, statusIcon;

                let customerType = '';
                if (balance < 0) {
                    // 欠款
                    balanceText = `欠款 ¥${Math.abs(balance).toFixed(2)}`;
                    balanceColor = '#ff4d4f';
                    statusText = '欠款';
                    statusIcon = '🚨';
                    customerType = 'debt';
                    debtCount++;
                    totalDebt += Math.abs(balance);
                } else if (balance < 100) {
                    // 余额不足
                    balanceText = `¥${balance.toFixed(2)}`;
                    balanceColor = '#ff4d4f';
                    statusText = '余额不足';
                    statusIcon = '⚠️';
                    customerType = 'low';
                    lowCount++;
                    totalBalance += balance;
                } else if (balance < 300) {
                    // 余额偏低
                    balanceText = `¥${balance.toFixed(2)}`;
                    balanceColor = '#faad14';
                    statusText = '余额偏低';
                    statusIcon = '⚡';
                    customerType = 'normal';
                    normalCount++;
                    totalBalance += balance;
                } else {
                    // 余额充足
                    balanceText = `¥${balance.toFixed(2)}`;
                    balanceColor = '#52c41a';
                    statusText = '余额充足';
                    statusIcon = '✅';
                    customerType = 'good';
                    goodCount++;
                    totalBalance += balance;
                }

                // 根据筛选条件决定是否显示
                if (filterValue === 'all' || filterValue === customerType) {

                html += `
                    <div style="background: white; padding: 16px; border-radius: 8px; border-left: 4px solid ${balanceColor}; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: transform 0.2s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                            <div>
                                <div style="font-weight: bold; font-size: 16px; color: #333; margin-bottom: 4px;">${customer.name}</div>
                                <div style="font-size: 12px; color: #666; display: flex; align-items: center; gap: 4px;">
                                    <span>${statusIcon}</span>
                                    <span>${statusText}</span>
                                </div>
                            </div>
                            <div style="text-align: right;">
                                <div style="color: ${balanceColor}; font-size: 18px; font-weight: bold; margin-bottom: 4px;">${balanceText}</div>
                                <div style="font-size: 12px; color: #999;">单价: ¥${customer.pricePerPiece || 0}/个</div>
                            </div>
                        </div>
                        <div style="display: flex; gap: 8px; margin-top: 12px;">
                            <button class="btn" style="flex: 1; padding: 6px 8px; font-size: 12px; background: #52c41a; color: white; border-radius: 4px;" onclick="quickRecharge(${customer.id})">💰 充值</button>
                            <button class="btn" style="flex: 1; padding: 6px 8px; font-size: 12px; background: #ff4d4f; color: white; border-radius: 4px;" onclick="addDebt(${customer.id})">📝 欠款</button>
                            <button class="btn" style="padding: 6px 8px; font-size: 12px; background: #1890ff; color: white; border-radius: 4px;" onclick="editCustomer(${customer.id})">✏️</button>
                        </div>
                    </div>
                `;
                }
            });

            container.innerHTML = html;

            // 更新统计信息
            if (summaryContainer) {
                let summaryHtml = '';

                if (debtCount > 0) {
                    summaryHtml += `<span style="color: #ff4d4f; font-weight: bold;">🚨 欠款: ${debtCount}人 (¥${totalDebt.toFixed(2)})</span>`;
                }
                if (lowCount > 0) {
                    summaryHtml += `<span style="color: #ff4d4f;">⚠️ 不足: ${lowCount}人</span>`;
                }
                if (normalCount > 0) {
                    summaryHtml += `<span style="color: #faad14;">⚡ 偏低: ${normalCount}人</span>`;
                }
                if (goodCount > 0) {
                    summaryHtml += `<span style="color: #52c41a;">✅ 充足: ${goodCount}人</span>`;
                }

                summaryHtml += `<span style="color: #1890ff; font-weight: bold;">💰 总余额: ¥${(totalBalance - totalDebt).toFixed(2)}</span>`;

                summaryContainer.innerHTML = summaryHtml;
            }
        }

        // 更新余额预警
        function updateBalanceWarning() {
            const container = document.getElementById('balanceWarning');
            if (!container) return;

            const debtCustomers = customers.filter(c => (c.balance || 0) < 0);
            const lowBalanceCustomers = customers.filter(c => {
                const balance = c.balance || 0;
                return balance >= 0 && balance < 100;
            });

            let warnings = [];

            if (debtCustomers.length > 0) {
                const debtNames = debtCustomers.map(c => {
                    const debt = Math.abs(c.balance || 0);
                    return `${c.name}(欠款¥${debt.toFixed(2)})`;
                }).join('、');
                warnings.push(`
                    <div style="background: #fff1f0; border: 1px solid #ffa39e; padding: 8px; border-radius: 4px; color: #cf1322; margin-bottom: 8px;">
                        <strong>🚨 客户欠款提醒：</strong>
                        ${debtNames} 存在欠款，请及时催收或充值。
                    </div>
                `);
            }

            if (lowBalanceCustomers.length > 0) {
                const lowNames = lowBalanceCustomers.map(c => c.name).join('、');
                warnings.push(`
                    <div style="background: #fff2f0; border: 1px solid #ffccc7; padding: 8px; border-radius: 4px; color: #ff4d4f;">
                        <strong>⚠️ 余额不足提醒：</strong>
                        ${lowNames} 的账户余额不足100元，建议及时充值。
                    </div>
                `);
            }

            if (warnings.length > 0) {
                container.innerHTML = warnings.join('');
                container.style.display = 'block';
            } else {
                container.style.display = 'none';
            }
        }

        // 更新成本分析
        function updateCostAnalysis() {
            // 计算本月收入
            const now = new Date();
            const currentMonth = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0');

            const monthlyIncome = orders
                .filter(order => order.createTime.startsWith(currentMonth))
                .reduce((sum, order) => sum + order.totalPrice, 0);

            // 计算本月耗材成本（简化计算）
            const materialCost = materials.reduce((sum, material) => sum + (material.totalCost || 0), 0);

            // 计算毛利润和利润率
            const grossProfit = monthlyIncome - materialCost;
            const profitRate = monthlyIncome > 0 ? (grossProfit / monthlyIncome * 100) : 0;

            // 更新显示
            const materialCostEl = document.getElementById('materialCost');
            const grossProfitEl = document.getElementById('grossProfit');
            const profitRateEl = document.getElementById('profitRate');

            if (materialCostEl) materialCostEl.textContent = `¥${materialCost.toFixed(2)}`;
            if (grossProfitEl) grossProfitEl.textContent = `¥${grossProfit.toFixed(2)}`;
            if (profitRateEl) profitRateEl.textContent = `${profitRate.toFixed(1)}%`;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSystem();
        });
    </script>
</body>
</html>